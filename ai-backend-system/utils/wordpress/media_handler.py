# ai-backend-system/utils/wordpress/media_handler.py
"""
WordPress Media Handler for AutoGen agents.

This module provides comprehensive media upload and management capabilities
for WordPress, including image optimization, format validation, and batch operations.

Features:
- Image, video, audio, and document upload support
- Automatic image optimization and resizing
- File format validation and size limits
- Batch media operations
- Integration with WordPress REST API
"""

import asyncio
import logging
import mimetypes
import os
from io import BytesIO
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, BinaryIO
from PIL import Image
import aiofiles

from config import get_config
from .exceptions import MediaUploadError, ValidationError, ConfigurationError
from .external_url_handler import ExternalUrlHandler


class MediaHandler:
    """
    Handles media upload and management operations for WordPress.
    
    Provides methods for:
    - Uploading various media types (images, videos, audio, documents)
    - Image optimization and resizing
    - File validation and format checking
    - Batch media operations
    """
    
    def __init__(self, wordpress_client, config: Dict[str, Any] = None):
        """
        Initialize MediaHandler with WordPress client.

        Args:
            wordpress_client: WordPressClient instance
            config (Dict[str, Any]): Optional custom configuration
        """
        self.client = wordpress_client
        self.logger = logging.getLogger(__name__)

        # Load media configuration
        self._load_config(config)

        # Initialize external URL handler
        self.url_handler = ExternalUrlHandler(config)
    
    def _load_config(self, custom_config: Dict[str, Any] = None):
        """Load media configuration from config system."""
        try:
            full_config = get_config()
            self.config = full_config.get('wordpress', {}).get('media', {})
            
            # Override with custom config if provided
            if custom_config:
                self.config.update(custom_config)
                
        except Exception as e:
            raise ConfigurationError(f"Failed to load media configuration: {str(e)}")
    
    def _validate_file_size(self, file_size: int) -> bool:
        """
        Validate file size against configured limits.
        
        Args:
            file_size (int): File size in bytes
            
        Returns:
            bool: True if valid
            
        Raises:
            ValidationError: If file size exceeds limit
        """
        max_size_mb = self.config.get('max_file_size_mb', 50)
        max_size_bytes = max_size_mb * 1024 * 1024
        
        if file_size > max_size_bytes:
            raise ValidationError(f"File size {file_size / 1024 / 1024:.1f}MB exceeds limit of {max_size_mb}MB")
        
        return True
    
    def _validate_file_format(self, filename: str, file_type: str = None) -> str:
        """
        Validate file format against supported formats.
        
        Args:
            filename (str): Original filename
            file_type (str): Optional file type hint
            
        Returns:
            str: Detected file type category
            
        Raises:
            ValidationError: If format not supported
        """
        file_ext = Path(filename).suffix.lower().lstrip('.')
        
        # Get supported formats from config
        supported_images = self.config.get('supported_image_formats', ['jpg', 'jpeg', 'png', 'gif', 'webp'])
        supported_videos = self.config.get('supported_video_formats', ['mp4', 'avi', 'mov', 'wmv'])
        supported_audio = self.config.get('supported_audio_formats', ['mp3', 'wav', 'ogg'])
        supported_docs = self.config.get('supported_document_formats', ['pdf', 'doc', 'docx', 'txt'])
        
        # Determine file type
        if file_ext in supported_images:
            return 'image'
        elif file_ext in supported_videos:
            return 'video'
        elif file_ext in supported_audio:
            return 'audio'
        elif file_ext in supported_docs:
            return 'document'
        else:
            all_supported = supported_images + supported_videos + supported_audio + supported_docs
            raise ValidationError(f"Unsupported file format: {file_ext}. Supported: {all_supported}")
    
    async def _optimize_image(self, image_data: bytes, filename: str) -> bytes:
        """
        Optimize image size and quality.
        
        Args:
            image_data (bytes): Original image data
            filename (str): Image filename
            
        Returns:
            bytes: Optimized image data
        """
        optimization_config = self.config.get('image_optimization', {})
        
        if not optimization_config.get('enabled', True):
            return image_data
        
        try:
            # Open image
            image = Image.open(BytesIO(image_data))
            
            # Get optimization settings
            max_width = optimization_config.get('max_width', 1920)
            max_height = optimization_config.get('max_height', 1080)
            quality = optimization_config.get('quality', 85)
            
            # Resize if needed
            if image.width > max_width or image.height > max_height:
                image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
                self.logger.info(f"Resized image {filename} to {image.width}x{image.height}")
            
            # Convert to RGB if necessary (for JPEG)
            if image.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # Save optimized image
            output = BytesIO()
            file_ext = Path(filename).suffix.lower()
            
            if file_ext in ['.jpg', '.jpeg']:
                image.save(output, format='JPEG', quality=quality, optimize=True)
            elif file_ext == '.png':
                image.save(output, format='PNG', optimize=True)
            elif file_ext == '.webp':
                image.save(output, format='WEBP', quality=quality, optimize=True)
            else:
                # For other formats, save as original
                image.save(output, format=image.format or 'JPEG', quality=quality)
            
            optimized_data = output.getvalue()
            
            # Log optimization results
            original_size = len(image_data)
            optimized_size = len(optimized_data)
            savings = (original_size - optimized_size) / original_size * 100
            
            self.logger.info(f"Optimized image {filename}: {original_size} -> {optimized_size} bytes ({savings:.1f}% savings)")
            
            return optimized_data
            
        except Exception as e:
            self.logger.warning(f"Failed to optimize image {filename}: {str(e)}, using original")
            return image_data
    
    async def upload_media(
        self,
        file_data: Union[bytes, BinaryIO],
        filename: str,
        title: str = None,
        alt_text: str = None,
        caption: str = None,
        description: str = None,
        post_id: int = None
    ) -> Dict[str, Any]:
        """
        Upload a media file to WordPress.
        
        Args:
            file_data (Union[bytes, BinaryIO]): File data or file-like object
            filename (str): Original filename
            title (str): Media title
            alt_text (str): Alt text for images
            caption (str): Media caption
            description (str): Media description
            post_id (int): Associate with specific post
            
        Returns:
            Dict[str, Any]: Uploaded media data
            
        Raises:
            MediaUploadError: If upload fails
        """
        try:
            # Read file data if needed
            if hasattr(file_data, 'read'):
                file_content = await file_data.read() if hasattr(file_data, 'read') else file_data.read()
            else:
                file_content = file_data
            
            # Validate file
            self._validate_file_size(len(file_content))
            file_type = self._validate_file_format(filename)
            
            # Optimize images
            if file_type == 'image':
                file_content = await self._optimize_image(file_content, filename)
            
            # Prepare upload data
            files = {
                'file': {
                    'content': file_content,
                    'filename': filename
                }
            }
            
            # Prepare metadata
            data = {}
            if title:
                data['title'] = title
            if alt_text:
                data['alt_text'] = alt_text
            if caption:
                data['caption'] = caption
            if description:
                data['description'] = description
            if post_id:
                data['post'] = post_id
            
            # Upload to WordPress
            response = await self.client._make_request('POST', 'media', data=data, files=files)
            
            self.logger.info(f"Uploaded media: {response.get('id')} - {filename}")
            return response
            
        except Exception as e:
            raise MediaUploadError(f"Failed to upload media {filename}: {str(e)}")
    
    async def upload_from_file(
        self,
        file_path: Union[str, Path],
        title: str = None,
        alt_text: str = None,
        caption: str = None,
        description: str = None,
        post_id: int = None
    ) -> Dict[str, Any]:
        """
        Upload a media file from local filesystem.
        
        Args:
            file_path (Union[str, Path]): Path to local file
            title (str): Media title
            alt_text (str): Alt text for images
            caption (str): Media caption
            description (str): Media description
            post_id (int): Associate with specific post
            
        Returns:
            Dict[str, Any]: Uploaded media data
            
        Raises:
            MediaUploadError: If upload fails
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise MediaUploadError(f"File not found: {file_path}")
        
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                file_data = await f.read()
            
            return await self.upload_media(
                file_data=file_data,
                filename=file_path.name,
                title=title or file_path.stem,
                alt_text=alt_text,
                caption=caption,
                description=description,
                post_id=post_id
            )
            
        except Exception as e:
            raise MediaUploadError(f"Failed to upload file {file_path}: {str(e)}")
    
    async def get_media(
        self,
        media_id: int = None,
        per_page: int = 10,
        page: int = 1,
        media_type: str = None,
        parent: int = None
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """
        Retrieve media from WordPress.
        
        Args:
            media_id (int): Specific media ID to retrieve
            per_page (int): Number of media items per page
            page (int): Page number
            media_type (str): Filter by media type
            parent (int): Filter by parent post ID
            
        Returns:
            Union[Dict[str, Any], List[Dict[str, Any]]]: Media data
            
        Raises:
            MediaUploadError: If retrieval fails
        """
        try:
            if media_id:
                # Get specific media item
                response = await self.client._make_request('GET', f'media/{media_id}')
                self.logger.info(f"Retrieved media: {media_id}")
                return response
            else:
                # Get media list
                params = {
                    'per_page': min(per_page, 100),
                    'page': page
                }
                
                if media_type:
                    params['media_type'] = media_type
                if parent:
                    params['parent'] = parent
                
                response = await self.client._make_request('GET', 'media', params=params)
                self.logger.info(f"Retrieved {len(response)} media items")
                return response
                
        except Exception as e:
            raise MediaUploadError(f"Failed to retrieve media: {str(e)}")
    
    async def delete_media(self, media_id: int, force: bool = False) -> Dict[str, Any]:
        """
        Delete a media file from WordPress.
        
        Args:
            media_id (int): Media ID to delete
            force (bool): Whether to bypass trash and force deletion
            
        Returns:
            Dict[str, Any]: Deletion response
            
        Raises:
            MediaUploadError: If deletion fails
        """
        try:
            params = {'force': force} if force else {}
            response = await self.client._make_request('DELETE', f'media/{media_id}', params=params)
            
            self.logger.info(f"Deleted media: {media_id}")
            return response
            
        except Exception as e:
            raise MediaUploadError(f"Failed to delete media {media_id}: {str(e)}")

    # ==================== Batch Operations ====================

    async def upload_multiple_files(
        self,
        file_paths: List[Union[str, Path]],
        post_id: int = None,
        max_concurrent: int = 3
    ) -> List[Dict[str, Any]]:
        """
        Upload multiple files concurrently.

        Args:
            file_paths (List[Union[str, Path]]): List of file paths to upload
            post_id (int): Associate all files with specific post
            max_concurrent (int): Maximum concurrent uploads

        Returns:
            List[Dict[str, Any]]: List of upload results

        Raises:
            MediaUploadError: If batch upload fails
        """
        batch_config = self.client.config.get('batch', {})
        max_files = batch_config.get('max_media_per_batch', 10)

        if len(file_paths) > max_files:
            raise MediaUploadError(f"Batch size {len(file_paths)} exceeds limit of {max_files}")

        semaphore = asyncio.Semaphore(max_concurrent)

        async def upload_single_file(file_path):
            async with semaphore:
                try:
                    return await self.upload_from_file(file_path, post_id=post_id)
                except Exception as e:
                    self.logger.error(f"Failed to upload {file_path}: {str(e)}")
                    return {'error': str(e), 'file_path': str(file_path)}

        try:
            tasks = [upload_single_file(file_path) for file_path in file_paths]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            successful_uploads = [r for r in results if isinstance(r, dict) and 'error' not in r]
            failed_uploads = [r for r in results if isinstance(r, dict) and 'error' in r]

            self.logger.info(f"Batch upload completed: {len(successful_uploads)} successful, {len(failed_uploads)} failed")

            return results

        except Exception as e:
            raise MediaUploadError(f"Batch upload failed: {str(e)}")

    async def set_featured_image(self, post_id: int, media_id: int) -> Dict[str, Any]:
        """
        Set a media item as the featured image for a post.

        Args:
            post_id (int): Post ID
            media_id (int): Media ID to set as featured image

        Returns:
            Dict[str, Any]: Updated post data

        Raises:
            MediaUploadError: If operation fails
        """
        try:
            response = await self.client.update_post(post_id, featured_media=media_id)
            self.logger.info(f"Set featured image {media_id} for post {post_id}")
            return response

        except Exception as e:
            raise MediaUploadError(f"Failed to set featured image: {str(e)}")

    async def get_media_by_post(self, post_id: int) -> List[Dict[str, Any]]:
        """
        Get all media associated with a specific post.

        Args:
            post_id (int): Post ID

        Returns:
            List[Dict[str, Any]]: List of media items

        Raises:
            MediaUploadError: If retrieval fails
        """
        try:
            return await self.get_media(parent=post_id, per_page=100)

        except Exception as e:
            raise MediaUploadError(f"Failed to get media for post {post_id}: {str(e)}")

    # ==================== External URL Handling Methods ====================

    async def process_external_image_url(
        self,
        url: str,
        method: str = None,
        title: str = None,
        alt_text: str = None,
        caption: str = None,
        description: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process external image URL for WordPress usage.

        Args:
            url (str): External image URL
            method (str): Processing method ('direct_url', 'cloud_storage', 'local_upload')
            title (str): Image title
            alt_text (str): Alt text for accessibility
            caption (str): Image caption
            description (str): Image description
            **kwargs: Additional processing options

        Returns:
            Dict[str, Any]: Processing result with WordPress-ready URL and metadata

        Raises:
            MediaUploadError: If processing fails
        """
        try:
            # Get default method from configuration
            if not method:
                image_handling_config = self.config.get('image_handling', {})
                method = image_handling_config.get('default_method', 'direct_url')

            self.logger.info(f"Processing external image URL with method '{method}': {url}")

            # Process URL using external URL handler
            result = await self.url_handler.process_url_for_wordpress(url, method, **kwargs)

            # Add WordPress-specific metadata
            result['wordpress_metadata'] = {
                'title': title or self._extract_title_from_url(url),
                'alt_text': alt_text or title or self._extract_title_from_url(url),
                'caption': caption,
                'description': description,
                'processing_method': method,
                'processed_at': result['metadata']['processed_at']
            }

            self.logger.info(f"Successfully processed external image URL: {result['wordpress_url']}")
            return result

        except Exception as e:
            raise MediaUploadError(f"Failed to process external image URL {url}: {str(e)}")

    async def create_media_from_external_url(
        self,
        url: str,
        method: str = None,
        title: str = None,
        alt_text: str = None,
        caption: str = None,
        description: str = None,
        post_id: int = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create WordPress media entry from external image URL.

        For direct_url method: Creates a media entry with external URL reference
        For cloud_storage method: Uploads to cloud and creates media entry
        For local_upload method: Downloads, uploads to WordPress, creates media entry

        Args:
            url (str): External image URL
            method (str): Processing method
            title (str): Media title
            alt_text (str): Alt text for images
            caption (str): Media caption
            description (str): Media description
            post_id (int): Associate with specific post
            **kwargs: Additional options

        Returns:
            Dict[str, Any]: WordPress media data or custom media reference

        Raises:
            MediaUploadError: If creation fails
        """
        try:
            # Process the external URL
            processed_result = await self.process_external_image_url(
                url, method, title, alt_text, caption, description, **kwargs
            )

            # Handle based on processing method
            if processed_result['method'] == 'local_upload':
                # Use traditional upload method
                return await self.upload_from_file(
                    processed_result['local_path'],
                    title=title,
                    alt_text=alt_text,
                    caption=caption,
                    description=description,
                    post_id=post_id
                )
            else:
                # For direct_url and cloud_storage, create a custom media reference
                # Note: WordPress doesn't natively support external media entries,
                # so we return a structured response for the application to handle
                return {
                    'id': f"external_{hash(processed_result['wordpress_url']) % 1000000}",
                    'source_url': processed_result['wordpress_url'],
                    'title': {'rendered': processed_result['wordpress_metadata']['title']},
                    'alt_text': processed_result['wordpress_metadata']['alt_text'],
                    'caption': {'rendered': processed_result['wordpress_metadata'].get('caption', '')},
                    'description': {'rendered': processed_result['wordpress_metadata'].get('description', '')},
                    'media_type': 'image',
                    'mime_type': processed_result.get('content_type', 'image/jpeg'),
                    'external_url_data': processed_result,
                    'is_external': True,
                    'processing_method': processed_result['method']
                }

        except Exception as e:
            raise MediaUploadError(f"Failed to create media from external URL {url}: {str(e)}")

    async def validate_external_image_url(self, url: str) -> Dict[str, Any]:
        """
        Validate external image URL accessibility and properties.

        Args:
            url (str): Image URL to validate

        Returns:
            Dict[str, Any]: Validation result

        Raises:
            MediaUploadError: If validation fails
        """
        try:
            return await self.url_handler.validate_url(url)
        except Exception as e:
            raise MediaUploadError(f"Failed to validate external image URL {url}: {str(e)}")

    def _extract_title_from_url(self, url: str) -> str:
        """Extract a reasonable title from URL."""
        try:
            from urllib.parse import urlparse, unquote
            from pathlib import Path

            parsed_url = urlparse(url)
            path = unquote(parsed_url.path)
            filename = Path(path).stem

            if filename:
                # Clean up filename to make it more readable
                title = filename.replace('_', ' ').replace('-', ' ')
                return title.title()
            else:
                return f"Image from {parsed_url.netloc}"

        except Exception:
            return "External Image"

    async def batch_process_external_urls(
        self,
        urls: List[str],
        method: str = None,
        post_id: int = None,
        max_concurrent: int = 3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Process multiple external image URLs concurrently.

        Args:
            urls (List[str]): List of image URLs to process
            method (str): Processing method for all URLs
            post_id (int): Associate all images with specific post
            max_concurrent (int): Maximum concurrent processing
            **kwargs: Additional processing options

        Returns:
            List[Dict[str, Any]]: List of processing results

        Raises:
            MediaUploadError: If batch processing fails
        """
        batch_config = self.client.config.get('batch', {})
        max_urls = batch_config.get('max_media_per_batch', 10)

        if len(urls) > max_urls:
            raise MediaUploadError(f"Batch size {len(urls)} exceeds limit of {max_urls}")

        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_single_url(url):
            async with semaphore:
                try:
                    return await self.create_media_from_external_url(
                        url, method=method, post_id=post_id, **kwargs
                    )
                except Exception as e:
                    self.logger.error(f"Failed to process external URL {url}: {str(e)}")
                    return {'error': str(e), 'url': url}

        try:
            tasks = [process_single_url(url) for url in urls]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            successful_results = [r for r in results if isinstance(r, dict) and 'error' not in r]
            failed_results = [r for r in results if isinstance(r, dict) and 'error' in r]

            self.logger.info(f"Batch external URL processing completed: {len(successful_results)} successful, {len(failed_results)} failed")

            return results

        except Exception as e:
            raise MediaUploadError(f"Batch external URL processing failed: {str(e)}")

    async def get_image_handling_methods(self) -> Dict[str, Any]:
        """
        Get available image handling methods and their configuration.

        Returns:
            Dict[str, Any]: Available methods and their status
        """
        image_handling_config = self.config.get('image_handling', {})

        methods = {
            'direct_url': {
                'enabled': image_handling_config.get('direct_url', {}).get('enabled', True),
                'description': 'Embed external URLs directly in WordPress content',
                'pros': ['No storage required', 'Fast processing', 'Always up-to-date'],
                'cons': ['Dependent on external availability', 'No local control']
            },
            'cloud_storage': {
                'enabled': image_handling_config.get('cloud_storage', {}).get('enabled', False),
                'description': 'Upload images to cloud storage and reference URLs',
                'pros': ['Reliable availability', 'CDN benefits', 'Your control'],
                'cons': ['Storage costs', 'Upload time', 'Configuration required']
            },
            'local_upload': {
                'enabled': image_handling_config.get('local_upload', {}).get('enabled', True),
                'description': 'Download and upload images to WordPress media library',
                'pros': ['Full WordPress integration', 'Local control', 'Editing capabilities'],
                'cons': ['Storage usage', 'Processing time', 'Bandwidth usage']
            }
        }

        # Check cloud storage provider availability
        if methods['cloud_storage']['enabled']:
            cloud_config = image_handling_config.get('cloud_storage', {})
            providers = []
            if cloud_config.get('aws_s3', {}).get('enabled', False):
                providers.append('AWS S3')
            if cloud_config.get('google_cloud', {}).get('enabled', False):
                providers.append('Google Cloud Storage')
            if cloud_config.get('azure_blob', {}).get('enabled', False):
                providers.append('Azure Blob Storage')

            methods['cloud_storage']['available_providers'] = providers

        return {
            'default_method': image_handling_config.get('default_method', 'direct_url'),
            'methods': methods
        }
