# ai-backend-system/utils/wordpress/wordpress_client.py
"""
WordPress API Client for AutoGen agents.

This module provides a comprehensive WordPress REST API client that enables
AutoGen agents to interact with WordPress through async/await patterns,
following the existing AI backend system architecture.

Features:
- Multi-region WordPress site support
- Async/await API operations
- Comprehensive error handling and retry logic
- Rate limiting and authentication management
- Integration with existing configuration system
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aiohttp
import json
from urllib.parse import urljoin, quote

from config import get_config
from .exceptions import (
    WordPressError,
    AuthenticationError,
    RateLimitError,
    NetworkError,
    ConfigurationError,
    PostError,
    CommentError
)
from .auth import AuthenticationManager
from .monitoring import get_monitor


class RateLimiter:
    """Rate limiter for WordPress API requests."""
    
    def __init__(self, requests_per_minute: int = 60, burst_limit: int = 10):
        self.requests_per_minute = requests_per_minute
        self.burst_limit = burst_limit
        self.requests = []
        self.burst_count = 0
        self.last_reset = time.time()
    
    async def acquire(self):
        """Acquire permission to make a request."""
        now = time.time()
        
        # Reset burst counter every minute
        if now - self.last_reset >= 60:
            self.burst_count = 0
            self.last_reset = now
            self.requests = [req for req in self.requests if now - req < 60]
        
        # Check burst limit
        if self.burst_count >= self.burst_limit:
            await asyncio.sleep(1)
            return await self.acquire()
        
        # Check rate limit
        if len(self.requests) >= self.requests_per_minute:
            sleep_time = 60 - (now - self.requests[0])
            if sleep_time > 0:
                await asyncio.sleep(sleep_time)
                return await self.acquire()
        
        self.requests.append(now)
        self.burst_count += 1


class WordPressClient:
    """
    Comprehensive WordPress REST API client for AutoGen agents.
    
    Provides async/await methods for WordPress operations including:
    - Post management (create, read, update, delete)
    - Comment management and moderation
    - Media upload and management
    - Multi-region support
    - Rate limiting and error handling
    """
    
    def __init__(self, region_code: str = None, config: Dict[str, Any] = None):
        """
        Initialize WordPress client for a specific region.
        
        Args:
            region_code (str): Region code (e.g., 'au', 'us', 'de')
            config (Dict[str, Any]): Optional custom configuration
        """
        self.region_code = region_code
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self._load_config(config)
        
        # Initialize rate limiter
        rate_config = self.config.get('rate_limiting', {})
        self.rate_limiter = RateLimiter(
            requests_per_minute=rate_config.get('requests_per_minute', 60),
            burst_limit=rate_config.get('burst_limit', 10)
        )
        
        # Initialize session, authentication, and monitoring
        self.session = None
        self.auth_manager = None
        self.monitor = get_monitor(self.region_code)
    
    def _load_config(self, custom_config: Dict[str, Any] = None):
        """Load WordPress configuration from config system."""
        try:
            full_config = get_config()
            
            # Get WordPress wrapper config
            self.config = full_config.get('wordpress', {})
            
            # Get region-specific API config
            wordpress_apis = full_config.get('wordpress_apis', {})
            
            if self.region_code:
                if self.region_code not in wordpress_apis:
                    raise ConfigurationError(f"No WordPress API configuration found for region: {self.region_code}")
                self.api_config = wordpress_apis[self.region_code]
            else:
                # Use first available region if none specified
                if not wordpress_apis:
                    raise ConfigurationError("No WordPress API configurations found")
                self.region_code = list(wordpress_apis.keys())[0]
                self.api_config = wordpress_apis[self.region_code]
            
            # Override with custom config if provided
            if custom_config:
                self.config.update(custom_config)
                
        except Exception as e:
            raise ConfigurationError(f"Failed to load WordPress configuration: {str(e)}")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._close_session()
    
    async def _initialize_session(self):
        """Initialize aiohttp session with authentication."""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=self.api_config.get('timeout', 30))

            # Create SSL context that skips verification for testing
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # Create connector with SSL context
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            self.session = aiohttp.ClientSession(timeout=timeout, connector=connector)

            # Setup authentication manager
            self.auth_manager = AuthenticationManager(self.api_config)
    
    async def _close_session(self):
        """Close aiohttp session."""
        if self.session:
            await self.session.close()
            self.session = None
    

    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Dict[str, Any] = None,
        params: Dict[str, Any] = None,
        files: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Make authenticated request to WordPress API with rate limiting and retry logic.
        
        Args:
            method (str): HTTP method (GET, POST, PUT, DELETE)
            endpoint (str): API endpoint path
            data (Dict[str, Any]): Request body data
            params (Dict[str, Any]): Query parameters
            files (Dict[str, Any]): Files for upload
            
        Returns:
            Dict[str, Any]: API response data
            
        Raises:
            WordPressError: For various API errors
        """
        if not self.session:
            await self._initialize_session()
        
        # Apply rate limiting
        await self.rate_limiter.acquire()

        # Start monitoring request
        request_id = self.monitor.start_request(method, endpoint, data=data, params=params)

        # Build URL - handle query parameters properly
        base_url = self.api_config['api_url']
        if '?' in base_url:
            # Handle URLs with query parameters (e.g., index.php?rest_route=/wp/v2/)
            if base_url.endswith('/'):
                url = base_url + endpoint
            else:
                url = base_url + '/' + endpoint
        else:
            # Handle standard URLs (e.g., /wp-json/wp/v2/)
            url = urljoin(base_url, endpoint)
        
        # Prepare headers with authentication
        headers = await self.auth_manager.get_auth_headers()
        if not files:  # Don't set Content-Type for multipart uploads
            headers['Content-Type'] = 'application/json'
        
        # Prepare request data
        request_kwargs = {
            'headers': headers,
            'params': params
        }
        
        if files:
            # For file uploads, use FormData
            form_data = aiohttp.FormData()
            if data:
                for key, value in data.items():
                    form_data.add_field(key, str(value))
            for key, file_data in files.items():
                form_data.add_field(key, file_data['content'], filename=file_data['filename'])
            request_kwargs['data'] = form_data
        elif data:
            request_kwargs['json'] = data
        
        # Retry logic
        max_retries = self.api_config.get('max_retries', 3)
        retry_delay = self.api_config.get('retry_delay', 1.0)
        
        for attempt in range(max_retries + 1):
            try:
                async with self.session.request(method, url, **request_kwargs) as response:
                    response_text = await response.text()
                    
                    # Handle rate limiting
                    if response.status == 429:
                        retry_after = int(response.headers.get('Retry-After', 60))
                        if attempt < max_retries:
                            self.logger.warning(f"Rate limited, retrying after {retry_after} seconds")
                            await asyncio.sleep(retry_after)
                            continue
                        else:
                            error = RateLimitError("Rate limit exceeded", retry_after)
                            self.monitor.end_request(request_id, response.status, error=error)
                            raise error

                    # Handle authentication errors
                    if response.status == 401:
                        error = AuthenticationError("WordPress authentication failed")
                        self.monitor.end_request(request_id, response.status, error=error)
                        raise error

                    # Handle other client errors
                    if response.status >= 400:
                        try:
                            error_data = json.loads(response_text)
                            error_message = error_data.get('message', f'HTTP {response.status}')
                        except json.JSONDecodeError:
                            error_message = f'HTTP {response.status}: {response_text}'

                        if response.status < 500 or attempt == max_retries:
                            error = WordPressError(f"WordPress API error: {error_message}")
                            self.monitor.end_request(request_id, response.status, error=error)
                            raise error

                    # Parse successful response
                    if response.status < 400:
                        try:
                            response_data = json.loads(response_text) if response_text else {}
                            self.monitor.end_request(request_id, response.status, response_data)
                            return response_data
                        except json.JSONDecodeError:
                            response_data = {'raw_response': response_text}
                            self.monitor.end_request(request_id, response.status, response_data)
                            return response_data
                    
            except aiohttp.ClientError as e:
                if attempt == max_retries:
                    error = NetworkError(f"Network error: {str(e)}")
                    self.monitor.end_request(request_id, 0, error=error)
                    raise error

                self.logger.warning(f"Network error on attempt {attempt + 1}, retrying: {str(e)}")
                await asyncio.sleep(retry_delay * (2 ** attempt))  # Exponential backoff

        error = WordPressError("Max retries exceeded")
        self.monitor.end_request(request_id, 0, error=error)
        raise error

    # ==================== Helper Methods ====================

    async def _resolve_categories(self, categories: List[Union[int, str]]) -> List[int]:
        """
        Resolve category names to IDs, creating new categories if needed.

        Args:
            categories (List[Union[int, str]]): Category IDs or names

        Returns:
            List[int]: List of category IDs
        """
        category_ids = []

        for category in categories:
            if isinstance(category, int):
                category_ids.append(category)
            else:
                # Try to find existing category by name
                existing_categories = await self._make_request('GET', 'categories', params={'search': category})

                if existing_categories:
                    # Use first matching category
                    category_ids.append(existing_categories[0]['id'])
                else:
                    # Create new category
                    new_category = await self._make_request('POST', 'categories', data={'name': category})
                    category_ids.append(new_category['id'])

        return category_ids

    async def _resolve_tags(self, tags: List[Union[int, str]]) -> List[int]:
        """
        Resolve tag names to IDs, creating new tags if needed.

        Args:
            tags (List[Union[int, str]]): Tag IDs or names

        Returns:
            List[int]: List of tag IDs
        """
        tag_ids = []

        for tag in tags:
            if isinstance(tag, int):
                tag_ids.append(tag)
            else:
                # Try to find existing tag by name
                existing_tags = await self._make_request('GET', 'tags', params={'search': tag})

                if existing_tags:
                    # Use first matching tag
                    tag_ids.append(existing_tags[0]['id'])
                else:
                    # Create new tag
                    new_tag = await self._make_request('POST', 'tags', data={'name': tag})
                    tag_ids.append(new_tag['id'])

        return tag_ids


    # ==================== Post Management Methods ====================

    async def create_post(
        self,
        title: str,
        content: str,
        status: str = None,
        categories: List[Union[int, str]] = None,
        tags: List[Union[int, str]] = None,
        featured_media: int = None,
        excerpt: str = None,
        meta: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Create a new WordPress post.

        Args:
            title (str): Post title
            content (str): Post content (HTML)
            status (str): Post status (draft, published, private, pending)
            categories (List[Union[int, str]]): Category IDs or names
            tags (List[Union[int, str]]): Tag IDs or names
            featured_media (int): Featured image media ID
            excerpt (str): Post excerpt
            meta (Dict[str, Any]): Custom meta fields

        Returns:
            Dict[str, Any]: Created post data

        Raises:
            PostError: If post creation fails
        """
        try:
            # Prepare post data
            post_data = {
                'title': title,
                'content': content,
                'status': status or self.config.get('posts', {}).get('default_status', 'draft')
            }

            # Add optional fields
            if excerpt:
                post_data['excerpt'] = excerpt
            if featured_media:
                post_data['featured_media'] = featured_media

            # Handle categories
            if categories:
                category_ids = await self._resolve_categories(categories)
                post_data['categories'] = category_ids

            # Handle tags
            if tags:
                tag_ids = await self._resolve_tags(tags)
                post_data['tags'] = tag_ids

            # Handle meta fields
            if meta:
                post_data['meta'] = meta

            response = await self._make_request('POST', 'posts', data=post_data)

            # Track post creation
            self.monitor.track_post_operation('create', response.get('id'), title=title)

            self.logger.info(f"Created WordPress post: {response.get('id')} - {title}")
            return response

        except Exception as e:
            raise PostError(f"Failed to create post: {str(e)}")

    async def get_posts(
        self,
        per_page: int = 10,
        page: int = 1,
        status: str = None,
        categories: List[int] = None,
        tags: List[int] = None,
        search: str = None,
        after: datetime = None,
        before: datetime = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve WordPress posts with filtering options.

        Args:
            per_page (int): Number of posts per page (max 100)
            page (int): Page number
            status (str): Post status filter
            categories (List[int]): Category ID filters
            tags (List[int]): Tag ID filters
            search (str): Search term
            after (datetime): Posts after this date
            before (datetime): Posts before this date

        Returns:
            List[Dict[str, Any]]: List of post data

        Raises:
            PostError: If retrieval fails
        """
        try:
            params = {
                'per_page': min(per_page, 100),
                'page': page
            }

            # Add filters
            if status:
                params['status'] = status
            if categories:
                params['categories'] = ','.join(map(str, categories))
            if tags:
                params['tags'] = ','.join(map(str, tags))
            if search:
                params['search'] = search
            if after:
                params['after'] = after.isoformat()
            if before:
                params['before'] = before.isoformat()

            response = await self._make_request('GET', 'posts', params=params)

            self.logger.info(f"Retrieved {len(response)} WordPress posts")
            return response

        except Exception as e:
            raise PostError(f"Failed to retrieve posts: {str(e)}")

    async def get_post(self, post_id: int) -> Dict[str, Any]:
        """
        Retrieve a specific WordPress post by ID.

        Args:
            post_id (int): Post ID

        Returns:
            Dict[str, Any]: Post data

        Raises:
            PostError: If post not found or retrieval fails
        """
        try:
            response = await self._make_request('GET', f'posts/{post_id}')
            self.logger.info(f"Retrieved WordPress post: {post_id}")
            return response

        except Exception as e:
            raise PostError(f"Failed to retrieve post {post_id}: {str(e)}")

    async def update_post(
        self,
        post_id: int,
        title: str = None,
        content: str = None,
        status: str = None,
        categories: List[Union[int, str]] = None,
        tags: List[Union[int, str]] = None,
        featured_media: int = None,
        excerpt: str = None,
        meta: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Update an existing WordPress post.

        Args:
            post_id (int): Post ID to update
            title (str): New post title
            content (str): New post content
            status (str): New post status
            categories (List[Union[int, str]]): New categories
            tags (List[Union[int, str]]): New tags
            featured_media (int): New featured media ID
            excerpt (str): New excerpt
            meta (Dict[str, Any]): New meta fields

        Returns:
            Dict[str, Any]: Updated post data

        Raises:
            PostError: If update fails
        """
        try:
            post_data = {}

            # Add fields to update
            if title is not None:
                post_data['title'] = title
            if content is not None:
                post_data['content'] = content
            if status is not None:
                post_data['status'] = status
            if excerpt is not None:
                post_data['excerpt'] = excerpt
            if featured_media is not None:
                post_data['featured_media'] = featured_media

            # Handle categories
            if categories is not None:
                category_ids = await self._resolve_categories(categories)
                post_data['categories'] = category_ids

            # Handle tags
            if tags is not None:
                tag_ids = await self._resolve_tags(tags)
                post_data['tags'] = tag_ids

            # Handle meta fields
            if meta is not None:
                post_data['meta'] = meta

            response = await self._make_request('PUT', f'posts/{post_id}', data=post_data)

            # Track post update
            self.monitor.track_post_operation('update', post_id)

            self.logger.info(f"Updated WordPress post: {post_id}")
            return response

        except Exception as e:
            raise PostError(f"Failed to update post {post_id}: {str(e)}")

    async def delete_post(self, post_id: int, force: bool = False) -> Dict[str, Any]:
        """
        Delete a WordPress post.

        Args:
            post_id (int): Post ID to delete
            force (bool): Whether to bypass trash and force deletion

        Returns:
            Dict[str, Any]: Deletion response

        Raises:
            PostError: If deletion fails
        """
        try:
            params = {'force': force} if force else {}
            response = await self._make_request('DELETE', f'posts/{post_id}', params=params)

            # Track post deletion
            self.monitor.track_post_operation('delete', post_id)

            self.logger.info(f"Deleted WordPress post: {post_id}")
            return response

        except Exception as e:
            raise PostError(f"Failed to delete post {post_id}: {str(e)}")

    # ==================== Comment Management Methods ====================

    async def create_comment(
        self,
        post_id: int,
        content: str,
        author_name: str = None,
        author_email: str = None,
        author_url: str = None,
        parent: int = None,
        status: str = None
    ) -> Dict[str, Any]:
        """
        Create a new comment on a WordPress post.

        Args:
            post_id (int): ID of the post to comment on
            content (str): Comment content
            author_name (str): Comment author name
            author_email (str): Comment author email
            author_url (str): Comment author URL
            parent (int): Parent comment ID for replies
            status (str): Comment status (approved, hold, spam)

        Returns:
            Dict[str, Any]: Created comment data

        Raises:
            CommentError: If comment creation fails
        """
        try:
            comment_data = {
                'post': post_id,
                'content': content
            }

            # Only set status if explicitly provided
            if status is not None:
                comment_data['status'] = status

            # Add optional fields
            if author_name:
                comment_data['author_name'] = author_name
            if author_email:
                comment_data['author_email'] = author_email
            if author_url:
                comment_data['author_url'] = author_url
            if parent:
                comment_data['parent'] = parent

            response = await self._make_request('POST', 'comments', data=comment_data)

            # Track comment creation
            self.monitor.track_comment_operation('create', response.get('id'), post_id=post_id)

            self.logger.info(f"Created WordPress comment: {response.get('id')} on post {post_id}")
            return response

        except Exception as e:
            raise CommentError(f"Failed to create comment: {str(e)}")

    async def get_comments(
        self,
        post_id: int = None,
        per_page: int = 10,
        page: int = 1,
        status: str = None,
        search: str = None,
        after: datetime = None,
        before: datetime = None,
        parent: int = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve WordPress comments with filtering options.

        Args:
            post_id (int): Filter by post ID
            per_page (int): Number of comments per page (max 100)
            page (int): Page number
            status (str): Comment status filter
            search (str): Search term
            after (datetime): Comments after this date
            before (datetime): Comments before this date
            parent (int): Parent comment ID filter

        Returns:
            List[Dict[str, Any]]: List of comment data

        Raises:
            CommentError: If retrieval fails
        """
        try:
            params = {
                'per_page': min(per_page, 100),
                'page': page
            }

            # Add filters
            if post_id:
                params['post'] = post_id
            if status:
                params['status'] = status
            if search:
                params['search'] = search
            if after:
                params['after'] = after.isoformat()
            if before:
                params['before'] = before.isoformat()
            if parent is not None:
                params['parent'] = parent

            response = await self._make_request('GET', 'comments', params=params)

            self.logger.info(f"Retrieved {len(response)} WordPress comments")
            return response

        except Exception as e:
            raise CommentError(f"Failed to retrieve comments: {str(e)}")

    async def get_comment(self, comment_id: int) -> Dict[str, Any]:
        """
        Retrieve a specific WordPress comment by ID.

        Args:
            comment_id (int): Comment ID

        Returns:
            Dict[str, Any]: Comment data

        Raises:
            CommentError: If comment not found or retrieval fails
        """
        try:
            response = await self._make_request('GET', f'comments/{comment_id}')
            self.logger.info(f"Retrieved WordPress comment: {comment_id}")
            return response

        except Exception as e:
            raise CommentError(f"Failed to retrieve comment {comment_id}: {str(e)}")

    async def update_comment(
        self,
        comment_id: int,
        content: str = None,
        status: str = None,
        author_name: str = None,
        author_email: str = None,
        author_url: str = None
    ) -> Dict[str, Any]:
        """
        Update an existing WordPress comment.

        Args:
            comment_id (int): Comment ID to update
            content (str): New comment content
            status (str): New comment status
            author_name (str): New author name
            author_email (str): New author email
            author_url (str): New author URL

        Returns:
            Dict[str, Any]: Updated comment data

        Raises:
            CommentError: If update fails
        """
        try:
            comment_data = {}

            # Add fields to update
            if content is not None:
                comment_data['content'] = content
            if status is not None:
                comment_data['status'] = status
            if author_name is not None:
                comment_data['author_name'] = author_name
            if author_email is not None:
                comment_data['author_email'] = author_email
            if author_url is not None:
                comment_data['author_url'] = author_url

            response = await self._make_request('PUT', f'comments/{comment_id}', data=comment_data)

            self.logger.info(f"Updated WordPress comment: {comment_id}")
            return response

        except Exception as e:
            raise CommentError(f"Failed to update comment {comment_id}: {str(e)}")

    async def moderate_comment(self, comment_id: int, action: str) -> Dict[str, Any]:
        """
        Moderate a WordPress comment (approve, hold, spam, trash).

        Args:
            comment_id (int): Comment ID to moderate
            action (str): Moderation action (approved, hold, spam, trash)

        Returns:
            Dict[str, Any]: Updated comment data

        Raises:
            CommentError: If moderation fails
        """
        valid_actions = ['approved', 'hold', 'spam', 'trash']
        if action not in valid_actions:
            raise CommentError(f"Invalid moderation action: {action}. Must be one of: {valid_actions}")

        try:
            response = await self.update_comment(comment_id, status=action)

            # Track comment moderation
            self.monitor.track_comment_operation('moderate', comment_id, action=action)

            self.logger.info(f"Moderated WordPress comment {comment_id}: {action}")
            return response

        except Exception as e:
            raise CommentError(f"Failed to moderate comment {comment_id}: {str(e)}")

    async def delete_comment(self, comment_id: int, force: bool = False) -> Dict[str, Any]:
        """
        Delete a WordPress comment.

        Args:
            comment_id (int): Comment ID to delete
            force (bool): Whether to bypass trash and force deletion

        Returns:
            Dict[str, Any]: Deletion response

        Raises:
            CommentError: If deletion fails
        """
        try:
            params = {'force': force} if force else {}
            response = await self._make_request('DELETE', f'comments/{comment_id}', params=params)

            self.logger.info(f"Deleted WordPress comment: {comment_id}")
            return response

        except Exception as e:
            raise CommentError(f"Failed to delete comment {comment_id}: {str(e)}")

    # ==================== Media Management Integration ====================

    def get_media_handler(self):
        """
        Get MediaHandler instance for this client.

        Returns:
            MediaHandler: Media handler instance
        """
        from .media_handler import MediaHandler
        return MediaHandler(self)

    async def upload_media(self, *args, **kwargs):
        """Convenience method for media upload."""
        media_handler = self.get_media_handler()
        return await media_handler.upload_media(*args, **kwargs)

    async def upload_from_file(self, *args, **kwargs):
        """Convenience method for file upload."""
        media_handler = self.get_media_handler()
        return await media_handler.upload_from_file(*args, **kwargs)

    # ==================== Batch Operations ====================

    async def create_posts_batch(self, posts_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create multiple posts in batch.

        Args:
            posts_data (List[Dict[str, Any]]): List of post data dictionaries

        Returns:
            List[Dict[str, Any]]: List of created posts

        Raises:
            PostError: If batch creation fails
        """
        batch_config = self.config.get('batch', {})
        max_posts = batch_config.get('max_posts_per_batch', 20)

        if len(posts_data) > max_posts:
            raise PostError(f"Batch size {len(posts_data)} exceeds limit of {max_posts}")

        results = []
        for post_data in posts_data:
            try:
                result = await self.create_post(**post_data)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to create post in batch: {str(e)}")
                results.append({'error': str(e), 'post_data': post_data})

        return results

    async def moderate_comments_batch(
        self,
        comment_ids: List[int],
        action: str
    ) -> List[Dict[str, Any]]:
        """
        Moderate multiple comments in batch.

        Args:
            comment_ids (List[int]): List of comment IDs
            action (str): Moderation action

        Returns:
            List[Dict[str, Any]]: List of moderation results

        Raises:
            CommentError: If batch moderation fails
        """
        batch_config = self.config.get('batch', {})
        max_comments = batch_config.get('max_comments_per_batch', 50)

        if len(comment_ids) > max_comments:
            raise CommentError(f"Batch size {len(comment_ids)} exceeds limit of {max_comments}")

        results = []
        for comment_id in comment_ids:
            try:
                result = await self.moderate_comment(comment_id, action)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to moderate comment {comment_id}: {str(e)}")
                results.append({'error': str(e), 'comment_id': comment_id})

        return results


def create_wordpress_client(region_code: str = None, config: Dict[str, Any] = None) -> WordPressClient:
    """
    Factory function to create a WordPress client instance.

    Args:
        region_code (str): Region code for multi-region support
        config (Dict[str, Any]): Optional custom configuration

    Returns:
        WordPressClient: Configured WordPress client instance
    """
    return WordPressClient(region_code=region_code, config=config)
