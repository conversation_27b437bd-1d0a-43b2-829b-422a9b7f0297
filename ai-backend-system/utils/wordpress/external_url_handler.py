# ai-backend-system/utils/wordpress/external_url_handler.py
"""
External Image URL Handler for WordPress Media Management.

This module provides comprehensive handling of external image URLs without
requiring local storage, including validation, cloud storage integration,
and URL processing capabilities.

Features:
- Direct external URL validation and accessibility checking
- Cloud storage integration (AWS S3, Google Cloud, Azure Blob)
- URL processing and optimization
- Fallback mechanisms for inaccessible URLs
- Integration with WordPress REST API
"""

import asyncio
import logging
import os
import hashlib
import mimetypes
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from urllib.parse import urlparse, urljoin, quote
import aiohttp
import aiofiles
from PIL import Image
from io import BytesIO

from config import get_config
from .exceptions import (
    MediaUploadError,
    ValidationError,
    ConfigurationError,
    NetworkError
)


class ExternalUrlHandler:
    """
    Handles external image URLs for WordPress media management.
    
    Provides methods for:
    - URL validation and accessibility checking
    - Cloud storage integration and upload
    - URL processing and optimization
    - Fallback handling for inaccessible URLs
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize ExternalUrlHandler.
        
        Args:
            config (Dict[str, Any]): Optional custom configuration
        """
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self._load_config(config)
        
        # Initialize cloud storage clients
        self._cloud_clients = {}
        self._initialize_cloud_clients()
    
    def _load_config(self, custom_config: Dict[str, Any] = None):
        """Load external URL handler configuration."""
        try:
            full_config = get_config()
            wordpress_config = full_config.get('wordpress', {})
            media_config = wordpress_config.get('media', {})
            self.config = media_config.get('image_handling', {})
            
            # Override with custom config if provided
            if custom_config:
                self.config.update(custom_config)
                
        except Exception as e:
            raise ConfigurationError(f"Failed to load external URL handler configuration: {str(e)}")
    
    def _initialize_cloud_clients(self):
        """Initialize cloud storage clients based on configuration."""
        cloud_config = self.config.get('cloud_storage', {})
        
        if not cloud_config.get('enabled', False):
            return
        
        # Initialize AWS S3 client
        if cloud_config.get('aws_s3', {}).get('enabled', False):
            try:
                import boto3
                s3_config = cloud_config['aws_s3']
                self._cloud_clients['aws_s3'] = boto3.client(
                    's3',
                    aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID', s3_config.get('access_key_id')),
                    aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY', s3_config.get('secret_access_key')),
                    region_name=s3_config.get('region', 'us-east-1')
                )
                self.logger.info("AWS S3 client initialized")
            except ImportError:
                self.logger.warning("boto3 not installed, AWS S3 support disabled")
            except Exception as e:
                self.logger.error(f"Failed to initialize AWS S3 client: {e}")
        
        # Initialize Google Cloud Storage client
        if cloud_config.get('google_cloud', {}).get('enabled', False):
            try:
                from google.cloud import storage
                gc_config = cloud_config['google_cloud']
                credentials_path = gc_config.get('credentials_path')
                if credentials_path:
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = credentials_path
                
                self._cloud_clients['google_cloud'] = storage.Client(
                    project=gc_config.get('project_id')
                )
                self.logger.info("Google Cloud Storage client initialized")
            except ImportError:
                self.logger.warning("google-cloud-storage not installed, GCS support disabled")
            except Exception as e:
                self.logger.error(f"Failed to initialize Google Cloud Storage client: {e}")
        
        # Initialize Azure Blob Storage client
        if cloud_config.get('azure_blob', {}).get('enabled', False):
            try:
                from azure.storage.blob import BlobServiceClient
                azure_config = cloud_config['azure_blob']
                account_key = os.getenv('AZURE_STORAGE_KEY', azure_config.get('account_key'))
                
                self._cloud_clients['azure_blob'] = BlobServiceClient(
                    account_url=f"https://{azure_config['account_name']}.blob.core.windows.net",
                    credential=account_key
                )
                self.logger.info("Azure Blob Storage client initialized")
            except ImportError:
                self.logger.warning("azure-storage-blob not installed, Azure Blob support disabled")
            except Exception as e:
                self.logger.error(f"Failed to initialize Azure Blob Storage client: {e}")
    
    async def validate_url(self, url: str) -> Dict[str, Any]:
        """
        Validate external image URL accessibility and properties.
        
        Args:
            url (str): Image URL to validate
            
        Returns:
            Dict[str, Any]: Validation result with accessibility info
            
        Raises:
            ValidationError: If URL validation fails
        """
        validation_config = self.config.get('direct_url', {}).get('validation', {})
        
        result = {
            'url': url,
            'is_valid': False,
            'is_accessible': False,
            'content_type': None,
            'content_length': None,
            'image_dimensions': None,
            'response_time_ms': None,
            'error': None
        }
        
        try:
            # Basic URL validation
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValidationError(f"Invalid URL format: {url}")
            
            # Check URL length
            max_length = validation_config.get('max_url_length', 2048)
            if len(url) > max_length:
                raise ValidationError(f"URL too long: {len(url)} > {max_length}")
            
            # Check HTTPS requirement
            if validation_config.get('require_https', False) and parsed_url.scheme != 'https':
                raise ValidationError(f"HTTPS required but URL uses {parsed_url.scheme}")
            
            # Check domain restrictions
            domain = parsed_url.netloc.lower()
            allowed_domains = validation_config.get('allowed_domains', [])
            blocked_domains = validation_config.get('blocked_domains', [])
            
            if allowed_domains and not any(domain.endswith(allowed) for allowed in allowed_domains):
                raise ValidationError(f"Domain not in allowed list: {domain}")
            
            if blocked_domains and any(domain.endswith(blocked) for blocked in blocked_domains):
                raise ValidationError(f"Domain is blocked: {domain}")
            
            result['is_valid'] = True
            
            # Check accessibility if enabled
            if validation_config.get('check_accessibility', True):
                accessibility_result = await self._check_url_accessibility(url, validation_config)
                result.update(accessibility_result)
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.warning(f"URL validation failed for {url}: {e}")
        
        return result
    
    async def _check_url_accessibility(self, url: str, validation_config: Dict[str, Any]) -> Dict[str, Any]:
        """Check if URL is accessible and get metadata."""
        result = {
            'is_accessible': False,
            'content_type': None,
            'content_length': None,
            'response_time_ms': None
        }
        
        timeout = validation_config.get('timeout_seconds', 10)
        start_time = datetime.now()
        
        try:
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            headers = {
                'User-Agent': 'Mozilla/5.0 (compatible; WordPress-Media-Handler/1.0)',
                'Accept': 'image/*,*/*;q=0.8'
            }
            
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.head(url, headers=headers) as response:
                    response_time = (datetime.now() - start_time).total_seconds() * 1000
                    result['response_time_ms'] = round(response_time, 2)
                    
                    if response.status == 200:
                        result['is_accessible'] = True
                        result['content_type'] = response.headers.get('content-type', '').lower()
                        result['content_length'] = response.headers.get('content-length')
                        
                        # Validate content type
                        if result['content_type'] and not result['content_type'].startswith('image/'):
                            self.logger.warning(f"URL does not return image content: {result['content_type']}")
                    else:
                        self.logger.warning(f"URL not accessible: HTTP {response.status}")
                        
        except asyncio.TimeoutError:
            self.logger.warning(f"URL accessibility check timeout: {url}")
        except Exception as e:
            self.logger.warning(f"URL accessibility check failed: {e}")
        
        return result
    
    async def process_url_for_wordpress(
        self,
        url: str,
        method: str = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process external image URL for WordPress usage.
        
        Args:
            url (str): External image URL
            method (str): Processing method ('direct_url', 'cloud_storage', 'local_upload')
            **kwargs: Additional processing options
            
        Returns:
            Dict[str, Any]: Processing result with WordPress-ready URL and metadata
        """
        method = method or self.config.get('default_method', 'direct_url')
        
        self.logger.info(f"Processing URL with method '{method}': {url}")
        
        # Validate URL first
        validation_result = await self.validate_url(url)
        if not validation_result['is_valid']:
            raise ValidationError(f"URL validation failed: {validation_result.get('error', 'Unknown error')}")
        
        # Process based on method
        if method == 'direct_url':
            return await self._process_direct_url(url, validation_result, **kwargs)
        elif method == 'cloud_storage':
            return await self._process_cloud_storage(url, validation_result, **kwargs)
        elif method == 'local_upload':
            return await self._process_local_upload(url, validation_result, **kwargs)
        else:
            raise ValueError(f"Unknown processing method: {method}")
    
    async def _process_direct_url(
        self,
        url: str,
        validation_result: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Process URL for direct embedding."""
        direct_config = self.config.get('direct_url', {})
        processing_config = direct_config.get('processing', {})
        
        processed_url = url
        
        # Add cache busting if enabled
        if processing_config.get('add_cache_busting', False):
            separator = '&' if '?' in processed_url else '?'
            cache_param = f"cb={int(datetime.now().timestamp())}"
            processed_url = f"{processed_url}{separator}{cache_param}"
        
        # Add resize parameters if enabled
        if processing_config.get('resize_parameters', False):
            separator = '&' if '?' in processed_url else '?'
            width = kwargs.get('width', processing_config.get('default_width', 800))
            height = kwargs.get('height', processing_config.get('default_height', 600))
            resize_params = f"w={width}&h={height}"
            processed_url = f"{processed_url}{separator}{resize_params}"
        
        return {
            'method': 'direct_url',
            'original_url': url,
            'processed_url': processed_url,
            'wordpress_url': processed_url,
            'is_accessible': validation_result.get('is_accessible', False),
            'content_type': validation_result.get('content_type'),
            'metadata': {
                'processing_method': 'direct_url',
                'validation_result': validation_result,
                'processed_at': datetime.now().isoformat()
            }
        }

    async def _process_cloud_storage(
        self,
        url: str,
        validation_result: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Process URL by uploading to cloud storage."""
        cloud_config = self.config.get('cloud_storage', {})

        if not cloud_config.get('enabled', False):
            raise ConfigurationError("Cloud storage is not enabled")

        # Download image data
        image_data = await self._download_image_data(url)

        # Determine which cloud provider to use
        cloud_provider = self._get_active_cloud_provider()
        if not cloud_provider:
            raise ConfigurationError("No active cloud storage provider configured")

        # Upload to cloud storage
        cloud_url = await self._upload_to_cloud(image_data, url, cloud_provider, **kwargs)

        return {
            'method': 'cloud_storage',
            'original_url': url,
            'processed_url': cloud_url,
            'wordpress_url': cloud_url,
            'cloud_provider': cloud_provider,
            'is_accessible': True,
            'content_type': validation_result.get('content_type'),
            'metadata': {
                'processing_method': 'cloud_storage',
                'cloud_provider': cloud_provider,
                'validation_result': validation_result,
                'processed_at': datetime.now().isoformat()
            }
        }

    async def _process_local_upload(
        self,
        url: str,
        validation_result: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Process URL by downloading and preparing for local upload."""
        local_config = self.config.get('local_upload', {})

        # Download image data
        image_data = await self._download_image_data(url)

        # Generate filename
        filename = self._generate_filename(url, validation_result.get('content_type'))

        # Save to temporary directory
        temp_dir = Path(local_config.get('temp_directory', 'temp_downloads'))
        temp_dir.mkdir(exist_ok=True)
        temp_path = temp_dir / filename

        async with aiofiles.open(temp_path, 'wb') as f:
            await f.write(image_data)

        return {
            'method': 'local_upload',
            'original_url': url,
            'local_path': str(temp_path),
            'filename': filename,
            'file_size': len(image_data),
            'content_type': validation_result.get('content_type'),
            'metadata': {
                'processing_method': 'local_upload',
                'validation_result': validation_result,
                'processed_at': datetime.now().isoformat()
            }
        }

    async def _download_image_data(self, url: str) -> bytes:
        """Download image data from URL."""
        try:
            timeout = aiohttp.ClientTimeout(total=30)
            headers = {
                'User-Agent': 'Mozilla/5.0 (compatible; WordPress-Media-Handler/1.0)',
                'Accept': 'image/*,*/*;q=0.8'
            }

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers=headers) as response:
                    if response.status != 200:
                        raise NetworkError(f"Failed to download image: HTTP {response.status}")

                    return await response.read()

        except Exception as e:
            raise NetworkError(f"Failed to download image from {url}: {str(e)}")

    def _get_active_cloud_provider(self) -> Optional[str]:
        """Get the first active cloud storage provider."""
        cloud_config = self.config.get('cloud_storage', {})

        providers = ['aws_s3', 'google_cloud', 'azure_blob']
        for provider in providers:
            if cloud_config.get(provider, {}).get('enabled', False):
                return provider

        return None

    async def _upload_to_cloud(
        self,
        image_data: bytes,
        original_url: str,
        provider: str,
        **kwargs
    ) -> str:
        """Upload image data to cloud storage."""
        cloud_config = self.config.get('cloud_storage', {})
        upload_config = cloud_config.get('upload_settings', {})

        # Generate cloud filename
        filename = self._generate_cloud_filename(original_url, upload_config)

        if provider == 'aws_s3':
            return await self._upload_to_s3(image_data, filename, **kwargs)
        elif provider == 'google_cloud':
            return await self._upload_to_gcs(image_data, filename, **kwargs)
        elif provider == 'azure_blob':
            return await self._upload_to_azure(image_data, filename, **kwargs)
        else:
            raise ValueError(f"Unsupported cloud provider: {provider}")

    async def _upload_to_s3(self, image_data: bytes, filename: str, **kwargs) -> str:
        """Upload to AWS S3."""
        if 'aws_s3' not in self._cloud_clients:
            raise ConfigurationError("AWS S3 client not initialized")

        s3_config = self.config.get('cloud_storage', {}).get('aws_s3', {})
        bucket_name = s3_config.get('bucket_name')

        if not bucket_name:
            raise ConfigurationError("AWS S3 bucket name not configured")

        try:
            # Upload to S3
            s3_client = self._cloud_clients['aws_s3']
            s3_client.put_object(
                Bucket=bucket_name,
                Key=filename,
                Body=image_data,
                ContentType=self._get_content_type_from_filename(filename)
            )

            # Generate URL
            custom_domain = s3_config.get('custom_domain')
            if custom_domain:
                return f"https://{custom_domain}/{filename}"
            else:
                region = s3_config.get('region', 'us-east-1')
                return f"https://{bucket_name}.s3.{region}.amazonaws.com/{filename}"

        except Exception as e:
            raise MediaUploadError(f"Failed to upload to S3: {str(e)}")

    async def _upload_to_gcs(self, image_data: bytes, filename: str, **kwargs) -> str:
        """Upload to Google Cloud Storage."""
        if 'google_cloud' not in self._cloud_clients:
            raise ConfigurationError("Google Cloud Storage client not initialized")

        gc_config = self.config.get('cloud_storage', {}).get('google_cloud', {})
        bucket_name = gc_config.get('bucket_name')

        if not bucket_name:
            raise ConfigurationError("Google Cloud Storage bucket name not configured")

        try:
            # Upload to GCS
            client = self._cloud_clients['google_cloud']
            bucket = client.bucket(bucket_name)
            blob = bucket.blob(filename)

            blob.upload_from_string(
                image_data,
                content_type=self._get_content_type_from_filename(filename)
            )

            # Generate URL
            custom_domain = gc_config.get('custom_domain')
            if custom_domain:
                return f"https://{custom_domain}/{filename}"
            else:
                return f"https://storage.googleapis.com/{bucket_name}/{filename}"

        except Exception as e:
            raise MediaUploadError(f"Failed to upload to Google Cloud Storage: {str(e)}")

    async def _upload_to_azure(self, image_data: bytes, filename: str, **kwargs) -> str:
        """Upload to Azure Blob Storage."""
        if 'azure_blob' not in self._cloud_clients:
            raise ConfigurationError("Azure Blob Storage client not initialized")

        azure_config = self.config.get('cloud_storage', {}).get('azure_blob', {})
        container_name = azure_config.get('container_name')
        account_name = azure_config.get('account_name')

        if not container_name or not account_name:
            raise ConfigurationError("Azure Blob Storage container or account name not configured")

        try:
            # Upload to Azure Blob
            blob_client = self._cloud_clients['azure_blob'].get_blob_client(
                container=container_name,
                blob=filename
            )

            blob_client.upload_blob(
                image_data,
                content_type=self._get_content_type_from_filename(filename),
                overwrite=True
            )

            # Generate URL
            custom_domain = azure_config.get('custom_domain')
            if custom_domain:
                return f"https://{custom_domain}/{filename}"
            else:
                return f"https://{account_name}.blob.core.windows.net/{container_name}/{filename}"

        except Exception as e:
            raise MediaUploadError(f"Failed to upload to Azure Blob Storage: {str(e)}")

    def _generate_filename(self, url: str, content_type: str = None) -> str:
        """Generate filename for local storage."""
        parsed_url = urlparse(url)
        url_filename = Path(parsed_url.path).name

        if url_filename and '.' in url_filename:
            return url_filename

        # Generate filename based on URL hash and content type
        url_hash = hashlib.md5(url.encode()).hexdigest()[:12]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Determine extension
        extension = '.jpg'  # Default
        if content_type:
            extension = mimetypes.guess_extension(content_type) or '.jpg'

        return f"external_image_{timestamp}_{url_hash}{extension}"

    def _generate_cloud_filename(self, url: str, upload_config: Dict[str, Any]) -> str:
        """Generate filename for cloud storage."""
        # Generate base filename
        base_filename = self._generate_filename(url)

        # Add prefix if configured
        prefix = upload_config.get('filename_prefix', '')
        if prefix:
            base_filename = f"{prefix}{base_filename}"

        # Organize by date if configured
        if upload_config.get('organize_by_date', False):
            now = datetime.now()
            date_path = f"{now.year:04d}/{now.month:02d}"
            base_filename = f"{date_path}/{base_filename}"

        return base_filename

    def _get_content_type_from_filename(self, filename: str) -> str:
        """Get content type from filename."""
        content_type, _ = mimetypes.guess_type(filename)
        return content_type or 'application/octet-stream'

    async def get_fallback_url(self, original_url: str, error: str = None) -> str:
        """Get fallback URL when original URL is inaccessible."""
        fallback_config = self.config.get('direct_url', {}).get('fallback', {})

        if fallback_config.get('use_placeholder', True):
            placeholder_url = fallback_config.get(
                'placeholder_url',
                "https://via.placeholder.com/800x600/cccccc/666666?text=Image+Not+Available"
            )
            return placeholder_url

        return original_url  # Return original URL as last resort

    async def cleanup_temp_files(self):
        """Clean up temporary files."""
        local_config = self.config.get('local_upload', {})
        temp_dir = Path(local_config.get('temp_directory', 'temp_downloads'))

        if not temp_dir.exists():
            return

        try:
            keep_recent = local_config.get('keep_recent_files', 5)
            files = list(temp_dir.glob("*"))
            files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            for file_path in files[keep_recent:]:
                if file_path.is_file():
                    file_path.unlink()
                    self.logger.info(f"Cleaned up temp file: {file_path.name}")

        except Exception as e:
            self.logger.warning(f"Failed to cleanup temp files: {e}")
