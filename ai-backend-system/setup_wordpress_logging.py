#!/usr/bin/env python3
"""
WordPress Logging Setup and Enhancement Script

This script sets up comprehensive logging for WordPress operations including:
- API request/response logging with detailed headers and payloads
- Authentication step-by-step logging
- Error condition logging with full stack traces
- Performance metrics and timing information
- Success/failure status tracking
- Rate limiting and retry logic logging

This enhances the existing WordPress monitoring system with additional
debugging and troubleshooting capabilities.
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# Add the ai-backend-system directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import get_config


class WordPressLoggingEnhancer:
    """Enhanced logging setup for comprehensive WordPress debugging."""
    
    def __init__(self):
        """Initialize the logging enhancer."""
        self.log_dir = Path("logs")
        self.log_dir.mkdir(exist_ok=True)
        
        # Load WordPress logging configuration
        self.config = get_config().get('wordpress', {}).get('logging', {})
        
        # Setup enhanced loggers
        self.setup_enhanced_logging()
    
    def setup_enhanced_logging(self):
        """Setup comprehensive WordPress logging system."""
        
        # Create specialized log files
        log_files = {
            'wordpress_api': 'wordpress_api_requests.log',
            'wordpress_auth': 'wordpress_authentication.log', 
            'wordpress_errors': 'wordpress_errors.log',
            'wordpress_performance': 'wordpress_performance.log',
            'wordpress_debug': 'wordpress_debug.log'
        }
        
        # Setup formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s() - %(message)s'
        )
        
        api_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(name)s] - %(message)s'
        )
        
        # Setup handlers for each log type
        for logger_name, log_file in log_files.items():
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.DEBUG)
            
            # Remove existing handlers
            logger.handlers.clear()
            
            # File handler with rotation
            file_handler = logging.handlers.RotatingFileHandler(
                self.log_dir / log_file,
                maxBytes=50*1024*1024,  # 50MB
                backupCount=5,
                encoding='utf-8'
            )
            
            if 'api' in logger_name:
                file_handler.setFormatter(api_formatter)
            else:
                file_handler.setFormatter(detailed_formatter)
            
            file_handler.setLevel(logging.DEBUG)
            logger.addHandler(file_handler)
            
            # Console handler for errors
            if 'error' in logger_name:
                console_handler = logging.StreamHandler(sys.stderr)
                console_handler.setFormatter(detailed_formatter)
                console_handler.setLevel(logging.ERROR)
                logger.addHandler(console_handler)
        
        # Setup WordPress-specific loggers
        self.setup_wordpress_loggers()
        
        print(f"✅ Enhanced WordPress logging setup complete")
        print(f"📁 Log directory: {self.log_dir.absolute()}")
        print(f"📝 Log files created: {list(log_files.values())}")
    
    def setup_wordpress_loggers(self):
        """Setup WordPress-specific logger configurations."""
        
        # WordPress API logger
        api_logger = logging.getLogger('utils.wordpress.wordpress_client')
        api_logger.setLevel(logging.DEBUG)
        
        # WordPress authentication logger
        auth_logger = logging.getLogger('utils.wordpress.auth')
        auth_logger.setLevel(logging.DEBUG)
        
        # WordPress monitoring logger
        monitor_logger = logging.getLogger('utils.wordpress.monitoring')
        monitor_logger.setLevel(logging.DEBUG)
        
        # WordPress media handler logger
        media_logger = logging.getLogger('utils.wordpress.media_handler')
        media_logger.setLevel(logging.DEBUG)
        
        print("✅ WordPress-specific loggers configured")
    
    def create_test_log_entries(self):
        """Create test log entries to verify logging setup."""
        
        # Test API logger
        api_logger = logging.getLogger('wordpress_api')
        api_logger.info("Test API request logging - setup verification")
        api_logger.debug("Test API debug message - detailed request info")
        
        # Test auth logger
        auth_logger = logging.getLogger('wordpress_auth')
        auth_logger.info("Test authentication logging - setup verification")
        auth_logger.debug("Test auth debug message - credential validation")
        
        # Test error logger
        error_logger = logging.getLogger('wordpress_errors')
        error_logger.error("Test error logging - setup verification")
        error_logger.warning("Test warning message - potential issue detected")
        
        # Test performance logger
        perf_logger = logging.getLogger('wordpress_performance')
        perf_logger.info("Test performance logging - setup verification")
        perf_logger.debug("Test performance debug - timing measurement")
        
        # Test debug logger
        debug_logger = logging.getLogger('wordpress_debug')
        debug_logger.debug("Test debug logging - setup verification")
        debug_logger.info("Test debug info - detailed operation trace")
        
        print("✅ Test log entries created successfully")
    
    def verify_log_files(self) -> bool:
        """Verify that all log files were created and are writable."""
        
        expected_files = [
            'wordpress_api_requests.log',
            'wordpress_authentication.log',
            'wordpress_errors.log', 
            'wordpress_performance.log',
            'wordpress_debug.log'
        ]
        
        all_good = True
        
        for log_file in expected_files:
            log_path = self.log_dir / log_file
            
            if log_path.exists():
                if log_path.stat().st_size > 0:
                    print(f"✅ {log_file}: Created and has content")
                else:
                    print(f"⚠️  {log_file}: Created but empty")
            else:
                print(f"❌ {log_file}: Not found")
                all_good = False
        
        return all_good
    
    def print_logging_configuration(self):
        """Print current WordPress logging configuration."""
        
        print("\n" + "=" * 60)
        print("📋 WORDPRESS LOGGING CONFIGURATION")
        print("=" * 60)
        
        print(f"Log Directory: {self.log_dir.absolute()}")
        print(f"Configuration Source: config/config.yaml")
        
        print("\nWordPress Logging Settings:")
        for key, value in self.config.items():
            print(f"  {key}: {value}")
        
        print("\nActive Loggers:")
        loggers = [
            'wordpress_api',
            'wordpress_auth', 
            'wordpress_errors',
            'wordpress_performance',
            'wordpress_debug',
            'utils.wordpress.wordpress_client',
            'utils.wordpress.auth',
            'utils.wordpress.monitoring',
            'utils.wordpress.media_handler'
        ]
        
        for logger_name in loggers:
            logger = logging.getLogger(logger_name)
            level = logging.getLevelName(logger.level)
            handler_count = len(logger.handlers)
            print(f"  {logger_name}: Level={level}, Handlers={handler_count}")
        
        print("\nLog File Rotation:")
        print("  Max Size: 50MB per file")
        print("  Backup Count: 5 files")
        print("  Encoding: UTF-8")
        
        print("\nLog Levels:")
        print("  DEBUG: Detailed diagnostic information")
        print("  INFO: General operational messages")
        print("  WARNING: Warning messages for potential issues")
        print("  ERROR: Error messages for failures")
        print("  CRITICAL: Critical errors that may cause system failure")


def main():
    """Main setup function."""
    print("🚀 WordPress Logging Enhancement Setup")
    print("=" * 60)
    
    try:
        # Initialize logging enhancer
        enhancer = WordPressLoggingEnhancer()
        
        # Create test log entries
        enhancer.create_test_log_entries()
        
        # Verify log files
        print("\n🔍 Verifying log file creation...")
        if enhancer.verify_log_files():
            print("✅ All log files created successfully")
        else:
            print("❌ Some log files were not created properly")
            return False
        
        # Print configuration
        enhancer.print_logging_configuration()
        
        print("\n🎉 WordPress logging enhancement completed successfully!")
        print("\nNext steps:")
        print("1. Run the WordPress test script: python test_wordpress_standalone.py")
        print("2. Check log files in the logs/ directory for detailed output")
        print("3. Monitor logs during WordPress operations for debugging")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up WordPress logging: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
