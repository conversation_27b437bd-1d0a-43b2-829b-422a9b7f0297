#!/usr/bin/env python3
"""
Debug URL construction for WordPress API
"""

from urllib.parse import urljoin

# Test the current URL construction logic
base_url = "http://localhost:8000/index.php?rest_route=/wp/v2/"
endpoint = "posts"

# This is what the current code does
constructed_url = urljoin(base_url, endpoint)
print(f"Base URL: {base_url}")
print(f"Endpoint: {endpoint}")
print(f"Constructed URL (urljoin): {constructed_url}")

# What we actually need
correct_url = "http://localhost:8000/index.php?rest_route=/wp/v2/posts"
print(f"Correct URL should be: {correct_url}")

# Test different approaches
print("\n--- Testing different URL construction approaches ---")

# Approach 1: Simple concatenation
if base_url.endswith('/'):
    simple_concat = base_url + endpoint
else:
    simple_concat = base_url + '/' + endpoint
print(f"Simple concatenation: {simple_concat}")

# Approach 2: Handle query parameters properly
if '?' in base_url:
    # Split base URL and query
    base_part, query_part = base_url.split('?', 1)
    if query_part.endswith('/'):
        proper_url = f"{base_part}?{query_part}{endpoint}"
    else:
        proper_url = f"{base_part}?{query_part}/{endpoint}"
else:
    proper_url = urljoin(base_url, endpoint)

print(f"Proper handling: {proper_url}")
