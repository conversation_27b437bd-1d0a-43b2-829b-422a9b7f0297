#!/usr/bin/env python3
"""
Test runner for external image handling functionality.

This script provides an easy way to run all tests related to external image handling,
including unit tests, integration tests, and performance benchmarks.

Usage:
    python run_external_image_tests.py                    # Run unit tests only
    python run_external_image_tests.py --integration      # Run all tests including integration
    python run_external_image_tests.py --performance      # Run performance benchmarks
    python run_external_image_tests.py --all              # Run everything
"""

import asyncio
import sys
import logging
import argparse
from pathlib import Path
from datetime import datetime

# Add the ai-backend-system directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.wordpress.wordpress_client import create_wordpress_client
from utils.wordpress.exceptions import WordPressError

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ExternalImageTestRunner:
    """Test runner for external image handling functionality."""
    
    def __init__(self):
        """Initialize test runner."""
        self.test_results = {
            'unit_tests': {'passed': 0, 'failed': 0, 'errors': []},
            'integration_tests': {'passed': 0, 'failed': 0, 'errors': []},
            'performance_tests': {'passed': 0, 'failed': 0, 'errors': [], 'metrics': {}}
        }
    
    async def run_unit_tests(self):
        """Run unit tests."""
        logger.info("🧪 Running unit tests...")
        
        try:
            import pytest
            
            # Run unit tests with pytest
            test_file = Path(__file__).parent / "test_external_image_handling.py"
            
            # Run only unit tests (exclude integration tests)
            result = pytest.main([
                str(test_file),
                "-v",
                "-m", "not integration",
                "--tb=short"
            ])
            
            if result == 0:
                self.test_results['unit_tests']['passed'] += 1
                logger.info("✅ Unit tests passed")
            else:
                self.test_results['unit_tests']['failed'] += 1
                self.test_results['unit_tests']['errors'].append("Unit tests failed")
                logger.error("❌ Unit tests failed")
            
        except ImportError:
            logger.error("❌ pytest not installed. Install with: pip install pytest")
            self.test_results['unit_tests']['failed'] += 1
            self.test_results['unit_tests']['errors'].append("pytest not available")
        except Exception as e:
            logger.error(f"❌ Unit test execution failed: {e}")
            self.test_results['unit_tests']['failed'] += 1
            self.test_results['unit_tests']['errors'].append(str(e))
    
    async def run_integration_tests(self):
        """Run integration tests."""
        logger.info("🔗 Running integration tests...")
        
        try:
            # Check WordPress connectivity first
            await self._check_wordpress_connectivity()
            
            import pytest
            
            # Run integration tests
            test_file = Path(__file__).parent / "test_external_image_handling.py"
            
            result = pytest.main([
                str(test_file),
                "-v",
                "-m", "integration",
                "--integration",
                "--tb=short"
            ])
            
            if result == 0:
                self.test_results['integration_tests']['passed'] += 1
                logger.info("✅ Integration tests passed")
            else:
                self.test_results['integration_tests']['failed'] += 1
                self.test_results['integration_tests']['errors'].append("Integration tests failed")
                logger.error("❌ Integration tests failed")
            
        except Exception as e:
            logger.error(f"❌ Integration test execution failed: {e}")
            self.test_results['integration_tests']['failed'] += 1
            self.test_results['integration_tests']['errors'].append(str(e))
    
    async def run_performance_tests(self):
        """Run performance benchmarks."""
        logger.info("⚡ Running performance tests...")
        
        try:
            # Check WordPress connectivity first
            await self._check_wordpress_connectivity()
            
            # Run custom performance tests
            await self._test_url_validation_performance()
            await self._test_processing_method_performance()
            await self._test_batch_processing_performance()
            
            logger.info("✅ Performance tests completed")
            
        except Exception as e:
            logger.error(f"❌ Performance test execution failed: {e}")
            self.test_results['performance_tests']['failed'] += 1
            self.test_results['performance_tests']['errors'].append(str(e))
    
    async def _check_wordpress_connectivity(self):
        """Check WordPress API connectivity."""
        logger.info("🔍 Checking WordPress connectivity...")
        
        try:
            async with create_wordpress_client('test') as client:
                # Try to get posts to verify connectivity
                posts = await client.get_posts(per_page=1)
                logger.info(f"✅ WordPress connected (found {len(posts)} posts)")
                
        except Exception as e:
            raise Exception(f"WordPress connectivity check failed: {e}")
    
    async def _test_url_validation_performance(self):
        """Test URL validation performance."""
        logger.info("📊 Testing URL validation performance...")
        
        test_urls = [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600",
            "https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=400&h=300"
        ]
        
        try:
            async with create_wordpress_client('test') as client:
                media_handler = client.get_media_handler()
                
                total_time = 0
                successful_validations = 0
                
                for url in test_urls:
                    start_time = datetime.now()
                    
                    try:
                        result = await media_handler.validate_external_image_url(url)
                        
                        validation_time = (datetime.now() - start_time).total_seconds()
                        total_time += validation_time
                        
                        if result['is_valid']:
                            successful_validations += 1
                        
                        logger.info(f"   URL validation: {validation_time:.2f}s - {url[:50]}...")
                        
                    except Exception as e:
                        logger.warning(f"   URL validation failed: {e}")
                
                avg_time = total_time / len(test_urls) if test_urls else 0
                success_rate = (successful_validations / len(test_urls)) * 100 if test_urls else 0
                
                self.test_results['performance_tests']['metrics']['url_validation'] = {
                    'average_time_seconds': avg_time,
                    'success_rate_percent': success_rate,
                    'total_urls_tested': len(test_urls)
                }
                
                logger.info(f"   Average validation time: {avg_time:.2f}s")
                logger.info(f"   Success rate: {success_rate:.1f}%")
                
                self.test_results['performance_tests']['passed'] += 1
                
        except Exception as e:
            logger.error(f"URL validation performance test failed: {e}")
            self.test_results['performance_tests']['failed'] += 1
            self.test_results['performance_tests']['errors'].append(str(e))
    
    async def _test_processing_method_performance(self):
        """Test processing method performance comparison."""
        logger.info("📊 Testing processing method performance...")
        
        test_url = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300"
        methods = ['direct_url', 'local_upload']
        
        try:
            async with create_wordpress_client('test') as client:
                media_handler = client.get_media_handler()
                method_performance = {}
                
                for method in methods:
                    logger.info(f"   Testing {method} method...")
                    start_time = datetime.now()
                    
                    try:
                        result = await media_handler.process_external_image_url(
                            url=test_url,
                            method=method,
                            title=f"Performance Test - {method}"
                        )
                        
                        processing_time = (datetime.now() - start_time).total_seconds()
                        method_performance[method] = {
                            'processing_time_seconds': processing_time,
                            'success': True,
                            'method': result['method']
                        }
                        
                        logger.info(f"     {method}: {processing_time:.2f}s")
                        
                        # Clean up local files if created
                        if method == 'local_upload' and 'local_path' in result:
                            local_path = Path(result['local_path'])
                            if local_path.exists():
                                local_path.unlink()
                        
                    except Exception as e:
                        logger.warning(f"     {method} failed: {e}")
                        method_performance[method] = {
                            'processing_time_seconds': None,
                            'success': False,
                            'error': str(e)
                        }
                
                self.test_results['performance_tests']['metrics']['method_performance'] = method_performance
                
                # Find fastest method
                successful_methods = {k: v for k, v in method_performance.items() if v['success']}
                if successful_methods:
                    fastest_method = min(successful_methods.keys(), 
                                       key=lambda x: successful_methods[x]['processing_time_seconds'])
                    logger.info(f"   Fastest method: {fastest_method}")
                
                self.test_results['performance_tests']['passed'] += 1
                
        except Exception as e:
            logger.error(f"Processing method performance test failed: {e}")
            self.test_results['performance_tests']['failed'] += 1
            self.test_results['performance_tests']['errors'].append(str(e))
    
    async def _test_batch_processing_performance(self):
        """Test batch processing performance."""
        logger.info("📊 Testing batch processing performance...")
        
        test_urls = [
            "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300",
            "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300",
            "https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=400&h=300"
        ]
        
        try:
            async with create_wordpress_client('test') as client:
                media_handler = client.get_media_handler()
                
                start_time = datetime.now()
                
                results = await media_handler.batch_process_external_urls(
                    urls=test_urls,
                    method='direct_url',
                    max_concurrent=2
                )
                
                batch_time = (datetime.now() - start_time).total_seconds()
                successful_results = [r for r in results if 'error' not in r]
                
                self.test_results['performance_tests']['metrics']['batch_processing'] = {
                    'total_time_seconds': batch_time,
                    'urls_processed': len(test_urls),
                    'successful_results': len(successful_results),
                    'success_rate_percent': (len(successful_results) / len(test_urls)) * 100,
                    'average_time_per_url': batch_time / len(test_urls)
                }
                
                logger.info(f"   Batch processing: {batch_time:.2f}s for {len(test_urls)} URLs")
                logger.info(f"   Success rate: {len(successful_results)}/{len(test_urls)}")
                logger.info(f"   Average per URL: {batch_time / len(test_urls):.2f}s")
                
                self.test_results['performance_tests']['passed'] += 1
                
        except Exception as e:
            logger.error(f"Batch processing performance test failed: {e}")
            self.test_results['performance_tests']['failed'] += 1
            self.test_results['performance_tests']['errors'].append(str(e))
    
    def print_summary(self):
        """Print test results summary."""
        logger.info("\n" + "=" * 60)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        total_passed = 0
        total_failed = 0
        
        for test_type, results in self.test_results.items():
            passed = results['passed']
            failed = results['failed']
            total_passed += passed
            total_failed += failed
            
            status_emoji = "✅" if failed == 0 else "❌"
            logger.info(f"{status_emoji} {test_type.upper()}: {passed} passed, {failed} failed")
            
            if results['errors']:
                for error in results['errors']:
                    logger.info(f"   Error: {error}")
        
        # Performance metrics summary
        if 'metrics' in self.test_results['performance_tests']:
            logger.info("\n📈 PERFORMANCE METRICS:")
            metrics = self.test_results['performance_tests']['metrics']
            
            if 'url_validation' in metrics:
                uv = metrics['url_validation']
                logger.info(f"   URL Validation: {uv['average_time_seconds']:.2f}s avg, {uv['success_rate_percent']:.1f}% success")
            
            if 'method_performance' in metrics:
                logger.info("   Method Performance:")
                for method, perf in metrics['method_performance'].items():
                    if perf['success']:
                        logger.info(f"     {method}: {perf['processing_time_seconds']:.2f}s")
                    else:
                        logger.info(f"     {method}: Failed")
            
            if 'batch_processing' in metrics:
                bp = metrics['batch_processing']
                logger.info(f"   Batch Processing: {bp['total_time_seconds']:.2f}s total, {bp['average_time_per_url']:.2f}s per URL")
        
        logger.info(f"\n🎯 OVERALL: {total_passed} passed, {total_failed} failed")
        
        return total_failed == 0


async def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="External Image Handling Test Runner")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting External Image Handling Tests")
    
    runner = ExternalImageTestRunner()
    
    try:
        # Always run unit tests
        await runner.run_unit_tests()
        
        # Run additional tests based on arguments
        if args.integration or args.all:
            await runner.run_integration_tests()
        
        if args.performance or args.all:
            await runner.run_performance_tests()
        
        # Print summary
        success = runner.print_summary()
        
        if success:
            logger.info("\n✅ All tests completed successfully!")
            return True
        else:
            logger.error("\n❌ Some tests failed!")
            return False
            
    except Exception as e:
        logger.error(f"💥 Test runner failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
