#!/usr/bin/env python3
"""
Comprehensive tests for external image handling functionality.

This test suite validates:
- External URL validation and processing
- Direct URL embedding functionality
- Cloud storage integration (when configured)
- Local upload processing
- WordPress client integration
- Error handling and fallback mechanisms
"""

import pytest
import asyncio
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

# Add the ai-backend-system directory to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.wordpress.external_url_handler import ExternalUrlHandler
from utils.wordpress.media_handler import MediaHandler
from utils.wordpress.wordpress_client import create_wordpress_client
from utils.wordpress.exceptions import (
    ValidationError,
    MediaUploadError,
    ConfigurationError,
    NetworkError
)


class TestExternalUrlHandler:
    """Test suite for ExternalUrlHandler class."""
    
    @pytest.fixture
    def url_handler(self):
        """Create ExternalUrlHandler instance for testing."""
        config = {
            'direct_url': {
                'enabled': True,
                'validation': {
                    'check_accessibility': True,
                    'timeout_seconds': 5,
                    'allowed_domains': [],
                    'blocked_domains': ['blocked-domain.com'],
                    'require_https': False,
                    'max_url_length': 2048
                },
                'processing': {
                    'add_cache_busting': False,
                    'resize_parameters': False
                },
                'fallback': {
                    'use_placeholder': True,
                    'placeholder_url': 'https://via.placeholder.com/800x600'
                }
            },
            'cloud_storage': {
                'enabled': False
            },
            'local_upload': {
                'enabled': True,
                'temp_directory': 'temp_test_downloads'
            }
        }
        return ExternalUrlHandler(config)
    
    @pytest.mark.asyncio
    async def test_url_validation_valid_url(self, url_handler):
        """Test validation of valid URLs."""
        valid_url = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4"
        
        with patch.object(url_handler, '_check_url_accessibility') as mock_check:
            mock_check.return_value = {
                'is_accessible': True,
                'content_type': 'image/jpeg',
                'content_length': '123456',
                'response_time_ms': 150.5
            }
            
            result = await url_handler.validate_url(valid_url)
            
            assert result['is_valid'] is True
            assert result['url'] == valid_url
            assert result['is_accessible'] is True
            assert result['content_type'] == 'image/jpeg'
    
    @pytest.mark.asyncio
    async def test_url_validation_invalid_format(self, url_handler):
        """Test validation of invalid URL formats."""
        invalid_url = "not-a-valid-url"
        
        result = await url_handler.validate_url(invalid_url)
        
        assert result['is_valid'] is False
        assert 'Invalid URL format' in result['error']
    
    @pytest.mark.asyncio
    async def test_url_validation_blocked_domain(self, url_handler):
        """Test validation of blocked domains."""
        blocked_url = "https://blocked-domain.com/image.jpg"
        
        result = await url_handler.validate_url(blocked_url)
        
        assert result['is_valid'] is False
        assert 'Domain is blocked' in result['error']
    
    @pytest.mark.asyncio
    async def test_url_validation_too_long(self, url_handler):
        """Test validation of URLs that are too long."""
        long_url = "https://example.com/" + "a" * 2050
        
        result = await url_handler.validate_url(long_url)
        
        assert result['is_valid'] is False
        assert 'URL too long' in result['error']
    
    @pytest.mark.asyncio
    async def test_process_direct_url(self, url_handler):
        """Test direct URL processing."""
        test_url = "https://example.com/image.jpg"
        
        with patch.object(url_handler, 'validate_url') as mock_validate:
            mock_validate.return_value = {
                'is_valid': True,
                'is_accessible': True,
                'content_type': 'image/jpeg'
            }
            
            result = await url_handler.process_url_for_wordpress(test_url, 'direct_url')
            
            assert result['method'] == 'direct_url'
            assert result['original_url'] == test_url
            assert result['wordpress_url'] == test_url
            assert result['is_accessible'] is True
    
    @pytest.mark.asyncio
    async def test_process_local_upload(self, url_handler):
        """Test local upload processing."""
        test_url = "https://example.com/image.jpg"
        mock_image_data = b"fake_image_data"
        
        with patch.object(url_handler, 'validate_url') as mock_validate, \
             patch.object(url_handler, '_download_image_data') as mock_download, \
             patch('aiofiles.open', create=True) as mock_open:
            
            mock_validate.return_value = {
                'is_valid': True,
                'is_accessible': True,
                'content_type': 'image/jpeg'
            }
            mock_download.return_value = mock_image_data
            
            # Mock file operations
            mock_file = AsyncMock()
            mock_open.return_value.__aenter__.return_value = mock_file
            
            result = await url_handler.process_url_for_wordpress(test_url, 'local_upload')
            
            assert result['method'] == 'local_upload'
            assert result['original_url'] == test_url
            assert 'local_path' in result
            assert 'filename' in result
            assert result['file_size'] == len(mock_image_data)
    
    @pytest.mark.asyncio
    async def test_download_image_data_success(self, url_handler):
        """Test successful image data download."""
        test_url = "https://example.com/image.jpg"
        mock_image_data = b"fake_image_data"
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.read.return_value = mock_image_data
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            result = await url_handler._download_image_data(test_url)
            
            assert result == mock_image_data
    
    @pytest.mark.asyncio
    async def test_download_image_data_failure(self, url_handler):
        """Test image data download failure."""
        test_url = "https://example.com/image.jpg"
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 404
            
            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(NetworkError):
                await url_handler._download_image_data(test_url)
    
    def test_generate_filename(self, url_handler):
        """Test filename generation."""
        test_url = "https://example.com/path/image.jpg"
        
        filename = url_handler._generate_filename(test_url, 'image/jpeg')
        
        assert filename == "image.jpg"
    
    def test_generate_filename_no_extension(self, url_handler):
        """Test filename generation without extension."""
        test_url = "https://example.com/path/image"
        
        filename = url_handler._generate_filename(test_url, 'image/jpeg')
        
        assert filename.startswith("external_image_")
        assert filename.endswith(".jpg")
    
    @pytest.mark.asyncio
    async def test_get_fallback_url(self, url_handler):
        """Test fallback URL generation."""
        original_url = "https://broken-link.com/image.jpg"
        
        fallback_url = await url_handler.get_fallback_url(original_url, "Image not accessible")
        
        assert fallback_url == "https://via.placeholder.com/800x600"


class TestMediaHandlerIntegration:
    """Test suite for MediaHandler integration with external URL handling."""
    
    @pytest.fixture
    def mock_wordpress_client(self):
        """Create mock WordPress client."""
        client = Mock()
        client.config = {
            'batch': {
                'max_media_per_batch': 10
            }
        }
        return client
    
    @pytest.fixture
    def media_handler(self, mock_wordpress_client):
        """Create MediaHandler instance for testing."""
        config = {
            'image_handling': {
                'default_method': 'direct_url',
                'direct_url': {'enabled': True},
                'cloud_storage': {'enabled': False},
                'local_upload': {'enabled': True}
            }
        }
        return MediaHandler(mock_wordpress_client, config)
    
    @pytest.mark.asyncio
    async def test_process_external_image_url(self, media_handler):
        """Test external image URL processing."""
        test_url = "https://example.com/image.jpg"
        
        with patch.object(media_handler.url_handler, 'process_url_for_wordpress') as mock_process:
            mock_process.return_value = {
                'method': 'direct_url',
                'original_url': test_url,
                'wordpress_url': test_url,
                'is_accessible': True,
                'metadata': {'processed_at': datetime.now().isoformat()}
            }
            
            result = await media_handler.process_external_image_url(
                url=test_url,
                title="Test Image",
                alt_text="Test alt text"
            )
            
            assert result['method'] == 'direct_url'
            assert result['wordpress_url'] == test_url
            assert 'wordpress_metadata' in result
            assert result['wordpress_metadata']['title'] == "Test Image"
            assert result['wordpress_metadata']['alt_text'] == "Test alt text"
    
    @pytest.mark.asyncio
    async def test_create_media_from_external_url_direct(self, media_handler):
        """Test creating media from external URL with direct method."""
        test_url = "https://example.com/image.jpg"
        
        with patch.object(media_handler, 'process_external_image_url') as mock_process:
            mock_process.return_value = {
                'method': 'direct_url',
                'original_url': test_url,
                'wordpress_url': test_url,
                'is_accessible': True,
                'content_type': 'image/jpeg',
                'wordpress_metadata': {
                    'title': 'Test Image',
                    'alt_text': 'Test alt text',
                    'caption': None,
                    'description': None
                }
            }
            
            result = await media_handler.create_media_from_external_url(
                url=test_url,
                method='direct_url',
                title="Test Image"
            )
            
            assert result['is_external'] is True
            assert result['processing_method'] == 'direct_url'
            assert result['source_url'] == test_url
            assert result['title']['rendered'] == 'Test Image'
    
    @pytest.mark.asyncio
    async def test_create_media_from_external_url_local_upload(self, media_handler):
        """Test creating media from external URL with local upload method."""
        test_url = "https://example.com/image.jpg"
        test_path = "/tmp/test_image.jpg"
        
        with patch.object(media_handler, 'process_external_image_url') as mock_process, \
             patch.object(media_handler, 'upload_from_file') as mock_upload:
            
            mock_process.return_value = {
                'method': 'local_upload',
                'local_path': test_path,
                'filename': 'test_image.jpg'
            }
            
            mock_upload.return_value = {
                'id': 123,
                'source_url': 'https://wordpress.com/wp-content/uploads/test_image.jpg'
            }
            
            result = await media_handler.create_media_from_external_url(
                url=test_url,
                method='local_upload',
                title="Test Image"
            )
            
            assert result['id'] == 123
            mock_upload.assert_called_once_with(
                test_path,
                title="Test Image",
                alt_text=None,
                caption=None,
                description=None,
                post_id=None
            )
    
    @pytest.mark.asyncio
    async def test_validate_external_image_url(self, media_handler):
        """Test external image URL validation."""
        test_url = "https://example.com/image.jpg"
        
        with patch.object(media_handler.url_handler, 'validate_url') as mock_validate:
            mock_validate.return_value = {
                'is_valid': True,
                'is_accessible': True,
                'content_type': 'image/jpeg'
            }
            
            result = await media_handler.validate_external_image_url(test_url)
            
            assert result['is_valid'] is True
            assert result['is_accessible'] is True
            mock_validate.assert_called_once_with(test_url)
    
    @pytest.mark.asyncio
    async def test_batch_process_external_urls(self, media_handler):
        """Test batch processing of external URLs."""
        test_urls = [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg"
        ]
        
        with patch.object(media_handler, 'create_media_from_external_url') as mock_create:
            mock_create.side_effect = [
                {'id': 'external_123', 'source_url': test_urls[0]},
                {'id': 'external_456', 'source_url': test_urls[1]}
            ]
            
            results = await media_handler.batch_process_external_urls(
                urls=test_urls,
                method='direct_url'
            )
            
            assert len(results) == 2
            assert all('error' not in result for result in results)
            assert mock_create.call_count == 2
    
    @pytest.mark.asyncio
    async def test_batch_process_external_urls_with_errors(self, media_handler):
        """Test batch processing with some failures."""
        test_urls = [
            "https://example.com/image1.jpg",
            "https://invalid-url"
        ]
        
        with patch.object(media_handler, 'create_media_from_external_url') as mock_create:
            mock_create.side_effect = [
                {'id': 'external_123', 'source_url': test_urls[0]},
                MediaUploadError("Invalid URL")
            ]
            
            results = await media_handler.batch_process_external_urls(
                urls=test_urls,
                method='direct_url'
            )
            
            assert len(results) == 2
            assert 'error' not in results[0]
            assert 'error' in results[1]
    
    def test_extract_title_from_url(self, media_handler):
        """Test title extraction from URL."""
        test_url = "https://example.com/path/beautiful_mountain_landscape.jpg"
        
        title = media_handler._extract_title_from_url(test_url)
        
        assert title == "Beautiful Mountain Landscape"
    
    def test_extract_title_from_url_no_filename(self, media_handler):
        """Test title extraction from URL without filename."""
        test_url = "https://example.com/path/"
        
        title = media_handler._extract_title_from_url(test_url)
        
        assert title == "Image from example.com"
    
    @pytest.mark.asyncio
    async def test_get_image_handling_methods(self, media_handler):
        """Test getting available image handling methods."""
        result = await media_handler.get_image_handling_methods()
        
        assert 'default_method' in result
        assert 'methods' in result
        assert 'direct_url' in result['methods']
        assert 'cloud_storage' in result['methods']
        assert 'local_upload' in result['methods']
        
        # Check method structure
        direct_url_method = result['methods']['direct_url']
        assert 'enabled' in direct_url_method
        assert 'description' in direct_url_method
        assert 'pros' in direct_url_method
        assert 'cons' in direct_url_method


class TestWordPressClientIntegration:
    """Test suite for WordPress client integration."""
    
    @pytest.mark.asyncio
    async def test_create_post_with_external_images(self):
        """Test creating posts with external images."""
        with patch('utils.wordpress.wordpress_client.create_wordpress_client') as mock_create_client:
            mock_client = AsyncMock()
            mock_media_handler = AsyncMock()
            
            # Mock media handler methods
            mock_media_handler.create_media_from_external_url.return_value = {
                'id': 'external_123',
                'source_url': 'https://processed-url.com/image.jpg',
                'external_url_data': {
                    'original_url': 'https://original-url.com/image.jpg',
                    'method': 'direct_url',
                    'is_accessible': True
                }
            }
            
            mock_client.get_media_handler.return_value = mock_media_handler
            mock_client.create_post.return_value = {
                'id': 456,
                'title': {'rendered': 'Test Post'},
                'link': 'https://wordpress.com/test-post'
            }
            
            mock_create_client.return_value = mock_client
            
            # Test the functionality
            client = mock_create_client()
            
            external_images = [{
                'url': 'https://original-url.com/image.jpg',
                'title': 'Test Image',
                'alt_text': 'Test alt text',
                'is_featured': True
            }]
            
            result = await client.create_post_with_external_images(
                title="Test Post",
                content="<p>Test content with <img src='https://original-url.com/image.jpg'></p>",
                external_images=external_images,
                image_handling_method='direct_url'
            )
            
            # Verify the result structure
            assert 'processed_external_images' in result
            assert 'image_handling_method' in result
            assert result['image_handling_method'] == 'direct_url'


class TestIntegrationWorkflow:
    """Integration tests for complete external image handling workflow."""

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_complete_direct_url_workflow(self):
        """Test complete workflow with direct URL method."""
        # This test requires actual WordPress instance and network access
        # Skip if not in integration test environment
        if not pytest.config.getoption("--integration", default=False):
            pytest.skip("Integration test requires --integration flag")

        try:
            async with create_wordpress_client('test') as client:
                # Test URL validation
                media_handler = client.get_media_handler()
                test_url = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600"

                validation_result = await media_handler.validate_external_image_url(test_url)
                assert validation_result['is_valid'] is True

                # Test image processing
                processed_result = await media_handler.process_external_image_url(
                    url=test_url,
                    method='direct_url',
                    title="Integration Test Image"
                )
                assert processed_result['method'] == 'direct_url'
                assert processed_result['wordpress_url'] == test_url

                # Test post creation
                external_images = [{
                    'url': test_url,
                    'title': 'Integration Test Image',
                    'alt_text': 'Test image for integration testing',
                    'is_featured': True
                }]

                post_result = await client.create_post_with_external_images(
                    title=f"Integration Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    content=f"<p>Integration test post with external image:</p><img src='{test_url}' alt='Test image'>",
                    external_images=external_images,
                    image_handling_method='direct_url',
                    status='draft'
                )

                assert post_result['id'] is not None
                assert len(post_result['processed_external_images']) == 1

                # Clean up - delete the test post
                await client.delete_post(post_result['id'], force=True)

        except Exception as e:
            pytest.fail(f"Integration test failed: {e}")

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_accessibility_and_fallback(self):
        """Test accessibility checking and fallback mechanisms."""
        if not pytest.config.getoption("--integration", default=False):
            pytest.skip("Integration test requires --integration flag")

        try:
            async with create_wordpress_client('test') as client:
                media_handler = client.get_media_handler()

                # Test with inaccessible URL
                inaccessible_url = "https://httpstat.us/404.jpg"

                validation_result = await media_handler.validate_external_image_url(inaccessible_url)

                # Should be valid format but not accessible
                assert validation_result['is_valid'] is True
                assert validation_result['is_accessible'] is False

                # Test fallback URL generation
                fallback_url = await media_handler.url_handler.get_fallback_url(
                    inaccessible_url,
                    "Image not accessible"
                )

                assert fallback_url != inaccessible_url
                assert "placeholder" in fallback_url.lower()

        except Exception as e:
            pytest.fail(f"Accessibility test failed: {e}")

    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_performance_benchmarking(self):
        """Test performance of different methods."""
        if not pytest.config.getoption("--integration", default=False):
            pytest.skip("Integration test requires --integration flag")

        try:
            async with create_wordpress_client('test') as client:
                media_handler = client.get_media_handler()
                test_url = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300"

                # Test direct URL method performance
                start_time = datetime.now()
                direct_result = await media_handler.process_external_image_url(
                    url=test_url,
                    method='direct_url',
                    title="Performance Test Direct"
                )
                direct_time = (datetime.now() - start_time).total_seconds()

                assert direct_result['method'] == 'direct_url'
                assert direct_time < 10.0  # Should be fast

                # Test local upload method performance
                start_time = datetime.now()
                local_result = await media_handler.process_external_image_url(
                    url=test_url,
                    method='local_upload',
                    title="Performance Test Local"
                )
                local_time = (datetime.now() - start_time).total_seconds()

                assert local_result['method'] == 'local_upload'
                assert local_time < 30.0  # Should complete within reasonable time

                # Direct URL should be faster than local upload
                assert direct_time < local_time

                # Clean up local file if created
                if 'local_path' in local_result:
                    local_path = Path(local_result['local_path'])
                    if local_path.exists():
                        local_path.unlink()

        except Exception as e:
            pytest.fail(f"Performance test failed: {e}")


def pytest_addoption(parser):
    """Add custom pytest options."""
    parser.addoption(
        "--integration",
        action="store_true",
        default=False,
        help="Run integration tests that require network access and WordPress instance"
    )


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
