#!/usr/bin/env python3
"""
WordPress REST API Standalone Test Script

This script demonstrates comprehensive WordPress REST API functionality by:
1. Creating a new post via the API using test user credentials
2. Adding a new comment to the created post via the API  
3. Using the admin user to review and approve both the post and comment

Features:
- Comprehensive logging of all operations
- Error handling and debugging information
- Authentication testing for both regular and admin users
- Post creation, comment creation, and moderation workflows
- Detailed API request/response logging
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# Add the ai-backend-system directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.wordpress import WordPressClient
from utils.wordpress.exceptions import (
    WordPressError,
    AuthenticationError,
    RateLimitError,
    PostError,
    CommentError,
    ConfigurationError
)
from config import get_config


class WordPressTestLogger:
    """Enhanced logging setup for WordPress testing."""
    
    def __init__(self, log_level: str = "DEBUG"):
        """Initialize comprehensive logging."""
        self.log_level = getattr(logging, log_level.upper())
        self.setup_logging()
    
    def setup_logging(self):
        """Setup detailed logging configuration."""
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler with simple format
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler with detailed format
        file_handler = logging.FileHandler(
            log_dir / "wordpress_test.log", 
            mode='w',  # Overwrite on each run
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)
        
        # WordPress-specific loggers
        wp_loggers = [
            'utils.wordpress',
            'wordpress.api',
            'wordpress.auth', 
            'wordpress.media',
            'wordpress.errors'
        ]
        
        for logger_name in wp_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.DEBUG)
        
        logging.info("=== WordPress API Test Session Started ===")
        logging.info(f"Log level: {logging.getLevelName(self.log_level)}")
        logging.info(f"Log file: {file_handler.baseFilename}")


class WordPressAPITester:
    """Comprehensive WordPress API testing class."""
    
    def __init__(self):
        """Initialize the tester."""
        self.logger = logging.getLogger(__name__)
        self.test_results = {
            'authentication': {'test_user': False, 'admin_user': False},
            'post_creation': False,
            'comment_creation': False,
            'post_moderation': False,
            'comment_moderation': False,
            'tag_management': {
                'get_tags': False,
                'create_tag': False,
                'update_tag': False,
                'delete_tag': False
            },
            'errors': []
        }
        
        # Test data
        self.test_post_data = {
            'title': f'Test Post - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
            'content': '''
            <h2>WordPress API Test Post</h2>
            <p>This post was created by the WordPress REST API test script to validate functionality.</p>

            <h3>Test Features</h3>
            <ul>
                <li>✅ API Authentication</li>
                <li>✅ Post Creation</li>
                <li>✅ Content Management</li>
                <li>✅ Comment System</li>
                <li>✅ Admin Moderation</li>
            </ul>

            <p><strong>Test completed at:</strong> {}</p>
            '''.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            'status': 'draft',
            'categories': [1],  # Use existing "Uncategorized" category ID
            'tags': [],  # Don't use tags to avoid permission issues
            'excerpt': 'A test post created by the WordPress API test script.'
        }
        
        self.test_comment_data = {
            'content': 'This is a test comment created by the WordPress API test script. It validates the comment creation and moderation functionality.',
            'author_name': 'API Test Bot',
            'author_email': '<EMAIL>'
            # Don't set status - let WordPress use default
        }
    
    async def test_authentication(self) -> bool:
        """Test authentication for both test and admin users."""
        self.logger.info("🔐 Testing Authentication...")
        
        # Test regular user authentication
        try:
            self.logger.info("Testing regular user authentication (chieh)...")
            async with WordPressClient(region_code='test') as client:
                # Try to get posts to test authentication
                posts = await client.get_posts(per_page=1)
                self.test_results['authentication']['test_user'] = True
                self.logger.info(f"✅ Regular user authentication successful. Found {len(posts)} posts.")
                
        except AuthenticationError as e:
            self.logger.error(f"❌ Regular user authentication failed: {e}")
            self.test_results['errors'].append(f"Regular user auth: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during regular user auth: {e}")
            self.test_results['errors'].append(f"Regular user auth unexpected: {e}")
            return False
        
        # Test admin user authentication
        try:
            self.logger.info("Testing admin user authentication (admin)...")
            async with WordPressClient(region_code='test_admin') as client:
                # Try to get posts to test authentication
                posts = await client.get_posts(per_page=1)
                self.test_results['authentication']['admin_user'] = True
                self.logger.info(f"✅ Admin user authentication successful. Found {len(posts)} posts.")
                
        except AuthenticationError as e:
            self.logger.error(f"❌ Admin user authentication failed: {e}")
            self.test_results['errors'].append(f"Admin user auth: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during admin user auth: {e}")
            self.test_results['errors'].append(f"Admin user auth unexpected: {e}")
            return False
        
        return True
    
    async def test_post_creation(self) -> Optional[Dict[str, Any]]:
        """Test creating a new post with the admin user (since regular user lacks permissions)."""
        self.logger.info("📝 Testing Post Creation...")

        try:
            # Try with admin user first since regular user may lack permissions
            async with WordPressClient(region_code='test_admin') as client:
                self.logger.info(f"Creating post with admin user: '{self.test_post_data['title']}'")

                post = await client.create_post(**self.test_post_data)

                self.test_results['post_creation'] = True
                self.logger.info(f"✅ Post created successfully!")
                self.logger.info(f"   Post ID: {post['id']}")
                self.logger.info(f"   Title: {post['title']['rendered']}")
                self.logger.info(f"   Status: {post['status']}")
                self.logger.info(f"   URL: {post['link']}")

                return post

        except PostError as e:
            self.logger.error(f"❌ Post creation failed with admin user: {e}")
            # Try with regular user as fallback
            try:
                async with WordPressClient(region_code='test') as client:
                    self.logger.info(f"Trying post creation with regular user: '{self.test_post_data['title']}'")
                    post = await client.create_post(**self.test_post_data)

                    self.test_results['post_creation'] = True
                    self.logger.info(f"✅ Post created successfully with regular user!")
                    self.logger.info(f"   Post ID: {post['id']}")
                    return post

            except Exception as e2:
                self.logger.error(f"❌ Post creation also failed with regular user: {e2}")
                self.test_results['errors'].append(f"Post creation: Admin failed ({e}), Regular user failed ({e2})")
                return None

            self.test_results['errors'].append(f"Post creation: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during post creation: {e}")
            self.test_results['errors'].append(f"Post creation unexpected: {e}")
            return None

    async def test_comment_creation(self, post_id: int) -> Optional[Dict[str, Any]]:
        """Test creating a comment on the created post."""
        self.logger.info("💬 Testing Comment Creation...")

        try:
            async with WordPressClient(region_code='test') as client:
                self.logger.info(f"Creating comment on post {post_id}")

                comment = await client.create_comment(
                    post_id=post_id,
                    **self.test_comment_data
                )

                self.test_results['comment_creation'] = True
                self.logger.info(f"✅ Comment created successfully!")
                self.logger.info(f"   Comment ID: {comment['id']}")
                self.logger.info(f"   Post ID: {comment['post']}")
                self.logger.info(f"   Status: {comment['status']}")
                self.logger.info(f"   Author: {comment['author_name']}")

                return comment

        except CommentError as e:
            self.logger.error(f"❌ Comment creation failed: {e}")
            self.test_results['errors'].append(f"Comment creation: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during comment creation: {e}")
            self.test_results['errors'].append(f"Comment creation unexpected: {e}")
            return None

    async def test_post_moderation(self, post_id: int) -> bool:
        """Test moderating the post using admin user."""
        self.logger.info("🔍 Testing Post Moderation...")

        try:
            async with WordPressClient(region_code='test_admin') as client:
                self.logger.info(f"Admin reviewing and publishing post {post_id}")

                # Update post status to published
                updated_post = await client.update_post(
                    post_id=post_id,
                    status='publish'
                )

                self.test_results['post_moderation'] = True
                self.logger.info(f"✅ Post moderation successful!")
                self.logger.info(f"   Post ID: {updated_post['id']}")
                self.logger.info(f"   New Status: {updated_post['status']}")
                self.logger.info(f"   Published URL: {updated_post['link']}")

                return True

        except PostError as e:
            self.logger.error(f"❌ Post moderation failed: {e}")
            self.test_results['errors'].append(f"Post moderation: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during post moderation: {e}")
            self.test_results['errors'].append(f"Post moderation unexpected: {e}")
            return False

    async def test_comment_moderation(self, comment_id: int) -> bool:
        """Test moderating the comment using admin user."""
        self.logger.info("✅ Testing Comment Moderation...")

        try:
            async with WordPressClient(region_code='test_admin') as client:
                self.logger.info(f"Admin approving comment {comment_id}")

                # Approve the comment
                moderated_comment = await client.moderate_comment(
                    comment_id=comment_id,
                    action='approved'
                )

                self.test_results['comment_moderation'] = True
                self.logger.info(f"✅ Comment moderation successful!")
                self.logger.info(f"   Comment ID: {moderated_comment['id']}")
                self.logger.info(f"   New Status: {moderated_comment['status']}")

                return True

        except CommentError as e:
            self.logger.error(f"❌ Comment moderation failed: {e}")
            self.test_results['errors'].append(f"Comment moderation: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during comment moderation: {e}")
            self.test_results['errors'].append(f"Comment moderation unexpected: {e}")
            return False

    async def test_tag_management(self) -> Dict[str, Any]:
        """Test comprehensive tag management functionality."""
        self.logger.info("🏷️ Testing Tag Management...")

        tag_results = {
            'get_tags': False,
            'create_tag': False,
            'update_tag': False,
            'delete_tag': False,
            'created_tag_id': None
        }

        try:
            async with WordPressClient(region_code='test_admin') as client:

                # Test 1: Get existing tags
                self.logger.info("📋 Testing get_tags...")
                try:
                    existing_tags = await client.get_tags(per_page=10)
                    tag_results['get_tags'] = True
                    self.logger.info(f"✅ Retrieved {len(existing_tags)} existing tags")

                    # Log some existing tags for reference
                    if existing_tags:
                        self.logger.info("   Existing tags:")
                        for tag in existing_tags[:3]:  # Show first 3 tags
                            self.logger.info(f"     - {tag['name']} (ID: {tag['id']})")

                except Exception as e:
                    self.logger.error(f"❌ Get tags failed: {e}")
                    self.test_results['errors'].append(f"Get tags: {e}")

                # Test 2: Create a new tag
                self.logger.info("➕ Testing create_tag...")
                test_tag_name = f"API-Test-Tag-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                try:
                    created_tag = await client.create_tag(
                        name=test_tag_name,
                        description="A test tag created by the WordPress API test script",
                        slug=f"api-test-tag-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                    )

                    tag_results['create_tag'] = True
                    tag_results['created_tag_id'] = created_tag['id']
                    self.logger.info(f"✅ Tag created successfully!")
                    self.logger.info(f"   Tag ID: {created_tag['id']}")
                    self.logger.info(f"   Tag Name: {created_tag['name']}")
                    self.logger.info(f"   Tag Slug: {created_tag['slug']}")

                except Exception as e:
                    self.logger.error(f"❌ Create tag failed: {e}")
                    self.test_results['errors'].append(f"Create tag: {e}")

                # Test 3: Update the created tag (if creation was successful)
                if tag_results['created_tag_id']:
                    self.logger.info("✏️ Testing update_tag...")
                    try:
                        updated_tag = await client.update_tag(
                            tag_id=tag_results['created_tag_id'],
                            name=f"{test_tag_name} (Updated)",
                            description="Updated description for the test tag"
                        )

                        tag_results['update_tag'] = True
                        self.logger.info(f"✅ Tag updated successfully!")
                        self.logger.info(f"   Updated Name: {updated_tag['name']}")
                        self.logger.info(f"   Updated Description: {updated_tag['description']}")

                    except Exception as e:
                        self.logger.error(f"❌ Update tag failed: {e}")
                        self.test_results['errors'].append(f"Update tag: {e}")
                else:
                    self.logger.warning("⚠️ Skipping tag update (no tag created)")

                # Test 4: Delete the created tag (if creation was successful)
                if tag_results['created_tag_id']:
                    self.logger.info("🗑️ Testing delete_tag...")
                    try:
                        deleted_tag = await client.delete_tag(
                            tag_id=tag_results['created_tag_id'],
                            force=True  # Force delete to bypass trash
                        )

                        tag_results['delete_tag'] = True
                        self.logger.info(f"✅ Tag deleted successfully!")
                        self.logger.info(f"   Deleted Tag ID: {tag_results['created_tag_id']}")

                    except Exception as e:
                        self.logger.error(f"❌ Delete tag failed: {e}")
                        self.test_results['errors'].append(f"Delete tag: {e}")
                else:
                    self.logger.warning("⚠️ Skipping tag deletion (no tag created)")

        except Exception as e:
            self.logger.error(f"❌ Unexpected error during tag management: {e}")
            self.test_results['errors'].append(f"Tag management unexpected: {e}")

        # Update main test results
        self.test_results['tag_management'] = tag_results

        # Log tag management summary
        passed_tag_tests = sum(1 for key in ['get_tags', 'create_tag', 'update_tag', 'delete_tag']
                              if tag_results[key])
        total_tag_tests = 4

        self.logger.info(f"🏷️ Tag Management Summary: {passed_tag_tests}/{total_tag_tests} tests passed")

        return tag_results

    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run the complete test suite."""
        self.logger.info("🚀 Starting Comprehensive WordPress API Test Suite")
        self.logger.info("=" * 60)

        # Test 1: Authentication
        auth_success = await self.test_authentication()
        if not auth_success:
            self.logger.error("❌ Authentication tests failed. Stopping test suite.")
            return self.test_results

        # Test 2: Post Creation
        created_post = await self.test_post_creation()
        if not created_post:
            self.logger.error("❌ Post creation failed. Continuing with other tests to gather more information.")
            # Continue with tests using existing post
            try:
                async with WordPressClient(region_code='test') as client:
                    posts = await client.get_posts(per_page=1)
                    if posts:
                        created_post = posts[0]  # Use existing post for testing
                        post_id = created_post['id']
                        self.logger.info(f"ℹ️ Using existing post {post_id} for remaining tests")
                    else:
                        self.logger.error("❌ No existing posts found. Cannot continue with tests.")
                        return self.test_results
            except Exception as e:
                self.logger.error(f"❌ Failed to get existing posts: {e}")
                return self.test_results
        else:
            post_id = created_post['id']

        # Test 3: Post Moderation (Admin) - Do this before comments
        if created_post and self.test_results['post_creation']:
            post_mod_success = await self.test_post_moderation(post_id)
            if not post_mod_success:
                self.logger.error("❌ Post moderation failed.")
        else:
            self.logger.warning("⚠️ Skipping post moderation (no post created)")

        # Test 4: Comment Creation (after post is published)
        created_comment = await self.test_comment_creation(post_id)
        if not created_comment:
            self.logger.error("❌ Comment creation failed. Continuing with comment moderation test.")

        comment_id = created_comment['id'] if created_comment else None

        # Test 5: Comment Moderation (Admin)
        if comment_id:
            comment_mod_success = await self.test_comment_moderation(comment_id)
            if not comment_mod_success:
                self.logger.error("❌ Comment moderation failed.")
        else:
            self.logger.warning("⚠️ Skipping comment moderation (no comment created)")

        # Test 6: Tag Management
        tag_results = await self.test_tag_management()
        if not any(tag_results.values()):
            self.logger.error("❌ All tag management tests failed.")

        return self.test_results

    def print_test_summary(self):
        """Print a comprehensive test summary."""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("📊 TEST SUMMARY")
        self.logger.info("=" * 60)

        # Authentication results
        auth_results = self.test_results['authentication']
        self.logger.info(f"🔐 Authentication:")
        self.logger.info(f"   Test User (chieh): {'✅ PASS' if auth_results['test_user'] else '❌ FAIL'}")
        self.logger.info(f"   Admin User (admin): {'✅ PASS' if auth_results['admin_user'] else '❌ FAIL'}")

        # Operation results
        operations = [
            ('📝 Post Creation', 'post_creation'),
            ('💬 Comment Creation', 'comment_creation'),
            ('🔍 Post Moderation', 'post_moderation'),
            ('✅ Comment Moderation', 'comment_moderation')
        ]

        for name, key in operations:
            status = '✅ PASS' if self.test_results[key] else '❌ FAIL'
            self.logger.info(f"{name}: {status}")

        # Tag management results
        tag_results = self.test_results.get('tag_management', {})
        if tag_results:
            self.logger.info(f"🏷️ Tag Management:")
            tag_operations = [
                ('   Get Tags', 'get_tags'),
                ('   Create Tag', 'create_tag'),
                ('   Update Tag', 'update_tag'),
                ('   Delete Tag', 'delete_tag')
            ]

            for name, key in tag_operations:
                status = '✅ PASS' if tag_results.get(key, False) else '❌ FAIL'
                self.logger.info(f"{name}: {status}")

        # Error summary
        if self.test_results['errors']:
            self.logger.info(f"\n❌ Errors encountered ({len(self.test_results['errors'])}):")
            for i, error in enumerate(self.test_results['errors'], 1):
                self.logger.info(f"   {i}. {error}")

            # Check for authentication-related errors and provide guidance
            auth_errors = [error for error in self.test_results['errors'] if 'authentication' in error.lower() or 'not allowed' in error.lower()]
            if auth_errors:
                self.logger.info(f"\n💡 AUTHENTICATION TROUBLESHOOTING:")
                self.logger.info(f"   The WordPress REST API requires proper authentication for write operations.")
                self.logger.info(f"   To fix authentication issues:")
                self.logger.info(f"   1. Enable Application Passwords in WordPress (Users → Profile → Application Passwords)")
                self.logger.info(f"   2. Generate an application password for the test users")
                self.logger.info(f"   3. Update config.yaml to use 'app_password' auth method")
                self.logger.info(f"   4. Or install a REST API authentication plugin")
                self.logger.info(f"   5. Ensure users have 'edit_posts' capability")
        else:
            self.logger.info("\n✅ No errors encountered!")

        # Overall result
        total_tests = 10  # 2 auth + 4 operations + 4 tag management
        passed_tests = (
            sum(auth_results.values()) +
            sum(1 for key in ['post_creation', 'comment_creation', 'post_moderation', 'comment_moderation']
                if self.test_results[key]) +
            sum(1 for key in ['get_tags', 'create_tag', 'update_tag', 'delete_tag']
                if self.test_results.get('tag_management', {}).get(key, False))
        )

        success_rate = (passed_tests / total_tests) * 100
        self.logger.info(f"\n🎯 Overall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")

        if success_rate == 100:
            self.logger.info("🎉 ALL TESTS PASSED! WordPress API is working perfectly.")
        elif success_rate >= 80:
            self.logger.info("✅ Most tests passed. WordPress API is mostly functional.")
        elif success_rate >= 50:
            self.logger.info("⚠️ Some tests failed. WordPress API has issues.")
        else:
            self.logger.info("❌ Many tests failed. WordPress API has serious issues.")


async def main():
    """Main test execution function."""
    print("🚀 WordPress REST API Standalone Test Script")
    print("=" * 60)

    # Setup logging
    test_logger = WordPressTestLogger(log_level="DEBUG")
    logger = logging.getLogger(__name__)

    try:
        # Verify configuration
        logger.info("🔧 Verifying configuration...")
        config = get_config()

        # Check if test configurations exist
        wordpress_apis = config.get('wordpress_apis', {})
        if 'test' not in wordpress_apis:
            logger.error("❌ 'test' configuration not found in wordpress_apis")
            return False

        if 'test_admin' not in wordpress_apis:
            logger.error("❌ 'test_admin' configuration not found in wordpress_apis")
            return False

        logger.info("✅ Configuration verified")

        # Run tests
        tester = WordPressAPITester()
        results = await tester.run_comprehensive_test()

        # Print summary
        tester.print_test_summary()

        # Return success status
        auth_success = all(results['authentication'].values())
        operations_success = all(results[key] for key in ['post_creation', 'comment_creation', 'post_moderation', 'comment_moderation'])
        tag_success = all(results.get('tag_management', {}).get(key, False) for key in ['get_tags', 'create_tag', 'update_tag', 'delete_tag'])

        return auth_success and operations_success and tag_success

    except ConfigurationError as e:
        logger.error(f"❌ Configuration error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        logger.exception("Full traceback:")
        return False


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())

    # Exit with appropriate code
    exit_code = 0 if success else 1
    print(f"\n🏁 Test completed with exit code: {exit_code}")
    sys.exit(exit_code)
