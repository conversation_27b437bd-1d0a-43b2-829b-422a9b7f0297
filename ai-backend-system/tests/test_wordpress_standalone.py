#!/usr/bin/env python3
"""
WordPress REST API Standalone Test Script

This script demonstrates comprehensive WordPress REST API functionality by:
1. Creating a new post via the API using test user credentials
2. Adding a new comment to the created post via the API  
3. Using the admin user to review and approve both the post and comment

Features:
- Comprehensive logging of all operations
- Error handling and debugging information
- Authentication testing for both regular and admin users
- Post creation, comment creation, and moderation workflows
- Detailed API request/response logging
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# Add the ai-backend-system directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.wordpress import WordPressClient
from utils.wordpress.exceptions import (
    WordPressError,
    AuthenticationError,
    RateLimitError,
    PostError,
    CommentError,
    ConfigurationError
)
from config import get_config


class WordPressTestLogger:
    """Enhanced logging setup for WordPress testing."""
    
    def __init__(self, log_level: str = "DEBUG"):
        """Initialize comprehensive logging."""
        self.log_level = getattr(logging, log_level.upper())
        self.setup_logging()
    
    def setup_logging(self):
        """Setup detailed logging configuration."""
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler with simple format
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)
        
        # File handler with detailed format
        file_handler = logging.FileHandler(
            log_dir / "wordpress_test.log", 
            mode='w',  # Overwrite on each run
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        root_logger.addHandler(file_handler)
        
        # WordPress-specific loggers
        wp_loggers = [
            'utils.wordpress',
            'wordpress.api',
            'wordpress.auth', 
            'wordpress.media',
            'wordpress.errors'
        ]
        
        for logger_name in wp_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.DEBUG)
        
        logging.info("=== WordPress API Test Session Started ===")
        logging.info(f"Log level: {logging.getLevelName(self.log_level)}")
        logging.info(f"Log file: {file_handler.baseFilename}")


class WordPressAPITester:
    """Comprehensive WordPress API testing class."""
    
    def __init__(self):
        """Initialize the tester."""
        self.logger = logging.getLogger(__name__)
        self.test_results = {
            'authentication': {'test_user': False, 'admin_user': False},
            'post_creation': False,
            'comment_creation': False,
            'post_moderation': False,
            'comment_moderation': False,
            'tag_management': {
                'get_tags': False,
                'create_tag': False,
                'update_tag': False,
                'delete_tag': False
            },
            'category_management': {
                'get_categories': False,
                'create_category': False,
                'update_category': False,
                'delete_category': False,
                'create_child_category': False
            },
            'category_integration': {
                'post_with_category': False,
                'category_assignment_verification': False,
                'published_post_category_retention': False
            },
            'media_upload': {
                'upload_jpeg': False,
                'upload_png': False,
                'upload_webp': False,
                'metadata_verification': False,
                'file_size_validation': False,
                'format_validation': False,
                'batch_upload': False
            },
            'featured_image': {
                'assignment_during_creation': False,
                'assignment_after_creation': False,
                'retention_through_publish': False,
                'verification_in_response': False,
                'url_accessibility': False
            },
            'errors': []
        }
        
        # Test data
        self.test_post_data = {
            'title': f'Test Post - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
            'content': '''
            <h2>WordPress API Test Post</h2>
            <p>This post was created by the WordPress REST API test script to validate functionality.</p>

            <h3>Test Features</h3>
            <ul>
                <li>✅ API Authentication</li>
                <li>✅ Post Creation</li>
                <li>✅ Content Management</li>
                <li>✅ Comment System</li>
                <li>✅ Admin Moderation</li>
            </ul>

            <p><strong>Test completed at:</strong> {}</p>
            '''.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            'status': 'draft',
            'categories': [1],  # Use existing "Uncategorized" category ID
            'tags': [],  # Don't use tags to avoid permission issues
            'excerpt': 'A test post created by the WordPress API test script.'
        }
        
        self.test_comment_data = {
            'content': 'This is a test comment created by the WordPress API test script. It validates the comment creation and moderation functionality.',
            'author_name': 'API Test Bot',
            'author_email': '<EMAIL>'
            # Don't set status - let WordPress use default
        }
    
    async def test_authentication(self) -> bool:
        """Test authentication for both test and admin users."""
        self.logger.info("🔐 Testing Authentication...")
        
        # Test regular user authentication
        try:
            self.logger.info("Testing regular user authentication (chieh)...")
            async with WordPressClient(region_code='test') as client:
                # Try to get posts to test authentication
                posts = await client.get_posts(per_page=1)
                self.test_results['authentication']['test_user'] = True
                self.logger.info(f"✅ Regular user authentication successful. Found {len(posts)} posts.")
                
        except AuthenticationError as e:
            self.logger.error(f"❌ Regular user authentication failed: {e}")
            self.test_results['errors'].append(f"Regular user auth: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during regular user auth: {e}")
            self.test_results['errors'].append(f"Regular user auth unexpected: {e}")
            return False
        
        # Test admin user authentication
        try:
            self.logger.info("Testing admin user authentication (admin)...")
            async with WordPressClient(region_code='test_admin') as client:
                # Try to get posts to test authentication
                posts = await client.get_posts(per_page=1)
                self.test_results['authentication']['admin_user'] = True
                self.logger.info(f"✅ Admin user authentication successful. Found {len(posts)} posts.")
                
        except AuthenticationError as e:
            self.logger.error(f"❌ Admin user authentication failed: {e}")
            self.test_results['errors'].append(f"Admin user auth: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during admin user auth: {e}")
            self.test_results['errors'].append(f"Admin user auth unexpected: {e}")
            return False
        
        return True
    
    async def test_post_creation(self) -> Optional[Dict[str, Any]]:
        """Test creating a new post with the admin user (since regular user lacks permissions)."""
        self.logger.info("📝 Testing Post Creation...")

        try:
            # Try with admin user first since regular user may lack permissions
            async with WordPressClient(region_code='test_admin') as client:
                self.logger.info(f"Creating post with admin user: '{self.test_post_data['title']}'")

                # Try to add a featured image to the basic post
                featured_media_id = None
                test_images_dir = Path(__file__).parent.parent / "utils" / "wordpress" / "example" / "test_images"

                if test_images_dir.exists():
                    try:
                        self.logger.info("🖼️ Attempting to add featured image to basic post...")
                        media_handler = client.get_media_handler()

                        # Use a simple test image
                        featured_image_file = test_images_dir / "test_medium_gradient_jpeg.jpg"
                        if not featured_image_file.exists():
                            featured_image_file = test_images_dir / "test_small_gradient_jpeg.jpg"

                        if featured_image_file.exists():
                            featured_media = await media_handler.upload_from_file(
                                file_path=featured_image_file,
                                title="Featured Image - Basic Post Test",
                                alt_text="Featured image for basic post creation test"
                            )
                            featured_media_id = featured_media['id']
                            self.logger.info(f"✅ Featured image uploaded for basic post: {featured_media_id}")

                    except Exception as e:
                        self.logger.warning(f"⚠️ Failed to add featured image to basic post: {e}")
                        # Continue without featured image

                # Create post data with optional featured image
                post_data = self.test_post_data.copy()
                if featured_media_id:
                    post_data['featured_media'] = featured_media_id

                post = await client.create_post(**post_data)

                self.test_results['post_creation'] = True
                self.logger.info(f"✅ Post created successfully!")
                self.logger.info(f"   Post ID: {post['id']}")
                self.logger.info(f"   Title: {post['title']['rendered']}")
                self.logger.info(f"   Status: {post['status']}")
                self.logger.info(f"   URL: {post['link']}")
                if featured_media_id:
                    self.logger.info(f"   Featured Media: {post.get('featured_media', 'Not assigned')}")

                return post

        except PostError as e:
            self.logger.error(f"❌ Post creation failed with admin user: {e}")
            # Try with regular user as fallback
            try:
                async with WordPressClient(region_code='test') as client:
                    self.logger.info(f"Trying post creation with regular user: '{self.test_post_data['title']}'")
                    post = await client.create_post(**self.test_post_data)

                    self.test_results['post_creation'] = True
                    self.logger.info(f"✅ Post created successfully with regular user!")
                    self.logger.info(f"   Post ID: {post['id']}")
                    return post

            except Exception as e2:
                self.logger.error(f"❌ Post creation also failed with regular user: {e2}")
                self.test_results['errors'].append(f"Post creation: Admin failed ({e}), Regular user failed ({e2})")
                return None

            self.test_results['errors'].append(f"Post creation: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during post creation: {e}")
            self.test_results['errors'].append(f"Post creation unexpected: {e}")
            return None

    async def test_comment_creation(self, post_id: int) -> Optional[Dict[str, Any]]:
        """Test creating a comment on the created post."""
        self.logger.info("💬 Testing Comment Creation...")

        try:
            async with WordPressClient(region_code='test') as client:
                self.logger.info(f"Creating comment on post {post_id}")

                comment = await client.create_comment(
                    post_id=post_id,
                    **self.test_comment_data
                )

                self.test_results['comment_creation'] = True
                self.logger.info(f"✅ Comment created successfully!")
                self.logger.info(f"   Comment ID: {comment['id']}")
                self.logger.info(f"   Post ID: {comment['post']}")
                self.logger.info(f"   Status: {comment['status']}")
                self.logger.info(f"   Author: {comment['author_name']}")

                return comment

        except CommentError as e:
            self.logger.error(f"❌ Comment creation failed: {e}")
            self.test_results['errors'].append(f"Comment creation: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during comment creation: {e}")
            self.test_results['errors'].append(f"Comment creation unexpected: {e}")
            return None

    async def test_post_moderation(self, post_id: int) -> bool:
        """Test moderating the post using admin user."""
        self.logger.info("🔍 Testing Post Moderation...")

        try:
            async with WordPressClient(region_code='test_admin') as client:
                self.logger.info(f"Admin reviewing and publishing post {post_id}")

                # Update post status to published
                updated_post = await client.update_post(
                    post_id=post_id,
                    status='publish'
                )

                self.test_results['post_moderation'] = True
                self.logger.info(f"✅ Post moderation successful!")
                self.logger.info(f"   Post ID: {updated_post['id']}")
                self.logger.info(f"   New Status: {updated_post['status']}")
                self.logger.info(f"   Published URL: {updated_post['link']}")

                return True

        except PostError as e:
            self.logger.error(f"❌ Post moderation failed: {e}")
            self.test_results['errors'].append(f"Post moderation: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during post moderation: {e}")
            self.test_results['errors'].append(f"Post moderation unexpected: {e}")
            return False

    async def test_comment_moderation(self, comment_id: int) -> bool:
        """Test moderating the comment using admin user."""
        self.logger.info("✅ Testing Comment Moderation...")

        try:
            async with WordPressClient(region_code='test_admin') as client:
                self.logger.info(f"Admin approving comment {comment_id}")

                # Approve the comment
                moderated_comment = await client.moderate_comment(
                    comment_id=comment_id,
                    action='approved'
                )

                self.test_results['comment_moderation'] = True
                self.logger.info(f"✅ Comment moderation successful!")
                self.logger.info(f"   Comment ID: {moderated_comment['id']}")
                self.logger.info(f"   New Status: {moderated_comment['status']}")

                return True

        except CommentError as e:
            self.logger.error(f"❌ Comment moderation failed: {e}")
            self.test_results['errors'].append(f"Comment moderation: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error during comment moderation: {e}")
            self.test_results['errors'].append(f"Comment moderation unexpected: {e}")
            return False

    async def test_tag_management(self) -> Dict[str, Any]:
        """Test comprehensive tag management functionality."""
        self.logger.info("🏷️ Testing Tag Management...")

        tag_results = {
            'get_tags': False,
            'create_tag': False,
            'update_tag': False,
            'delete_tag': False,
            'created_tag_id': None
        }

        try:
            async with WordPressClient(region_code='test_admin') as client:

                # Test 1: Get existing tags
                self.logger.info("📋 Testing get_tags...")
                try:
                    existing_tags = await client.get_tags(per_page=10)
                    tag_results['get_tags'] = True
                    self.logger.info(f"✅ Retrieved {len(existing_tags)} existing tags")

                    # Log some existing tags for reference
                    if existing_tags:
                        self.logger.info("   Existing tags:")
                        for tag in existing_tags[:3]:  # Show first 3 tags
                            self.logger.info(f"     - {tag['name']} (ID: {tag['id']})")

                except Exception as e:
                    self.logger.error(f"❌ Get tags failed: {e}")
                    self.test_results['errors'].append(f"Get tags: {e}")

                # Test 2: Create a new tag
                self.logger.info("➕ Testing create_tag...")
                test_tag_name = f"API-Test-Tag-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                try:
                    created_tag = await client.create_tag(
                        name=test_tag_name,
                        description="A test tag created by the WordPress API test script",
                        slug=f"api-test-tag-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                    )

                    tag_results['create_tag'] = True
                    tag_results['created_tag_id'] = created_tag['id']
                    self.logger.info(f"✅ Tag created successfully!")
                    self.logger.info(f"   Tag ID: {created_tag['id']}")
                    self.logger.info(f"   Tag Name: {created_tag['name']}")
                    self.logger.info(f"   Tag Slug: {created_tag['slug']}")

                except Exception as e:
                    self.logger.error(f"❌ Create tag failed: {e}")
                    self.test_results['errors'].append(f"Create tag: {e}")

                # Test 3: Update the created tag (if creation was successful)
                if tag_results['created_tag_id']:
                    self.logger.info("✏️ Testing update_tag...")
                    try:
                        updated_tag = await client.update_tag(
                            tag_id=tag_results['created_tag_id'],
                            name=f"{test_tag_name} (Updated)",
                            description="Updated description for the test tag"
                        )

                        tag_results['update_tag'] = True
                        self.logger.info(f"✅ Tag updated successfully!")
                        self.logger.info(f"   Updated Name: {updated_tag['name']}")
                        self.logger.info(f"   Updated Description: {updated_tag['description']}")

                    except Exception as e:
                        self.logger.error(f"❌ Update tag failed: {e}")
                        self.test_results['errors'].append(f"Update tag: {e}")
                else:
                    self.logger.warning("⚠️ Skipping tag update (no tag created)")

                # Test 4: Delete the created tag (if creation was successful)
                if tag_results['created_tag_id']:
                    self.logger.info("🗑️ Testing delete_tag...")
                    try:
                        deleted_tag = await client.delete_tag(
                            tag_id=tag_results['created_tag_id'],
                            force=True  # Force delete to bypass trash
                        )

                        tag_results['delete_tag'] = True
                        self.logger.info(f"✅ Tag deleted successfully!")
                        self.logger.info(f"   Deleted Tag ID: {tag_results['created_tag_id']}")

                    except Exception as e:
                        self.logger.error(f"❌ Delete tag failed: {e}")
                        self.test_results['errors'].append(f"Delete tag: {e}")
                else:
                    self.logger.warning("⚠️ Skipping tag deletion (no tag created)")

        except Exception as e:
            self.logger.error(f"❌ Unexpected error during tag management: {e}")
            self.test_results['errors'].append(f"Tag management unexpected: {e}")

        # Update main test results
        self.test_results['tag_management'] = tag_results

        # Log tag management summary
        passed_tag_tests = sum(1 for key in ['get_tags', 'create_tag', 'update_tag', 'delete_tag']
                              if tag_results[key])
        total_tag_tests = 4

        self.logger.info(f"🏷️ Tag Management Summary: {passed_tag_tests}/{total_tag_tests} tests passed")

        return tag_results

    async def test_category_management(self) -> Dict[str, Any]:
        """Test comprehensive category management functionality."""
        self.logger.info("📁 Testing Category Management...")

        category_results = {
            'get_categories': False,
            'create_category': False,
            'update_category': False,
            'delete_category': False,
            'create_child_category': False,
            'created_category_id': None,
            'created_child_category_id': None
        }

        try:
            async with WordPressClient(region_code='test_admin') as client:

                # Test 1: Get existing categories
                self.logger.info("📋 Testing get_categories...")
                try:
                    existing_categories = await client.get_categories(per_page=10)
                    category_results['get_categories'] = True
                    self.logger.info(f"✅ Retrieved {len(existing_categories)} existing categories")

                    # Log some existing categories for reference
                    if existing_categories:
                        self.logger.info("   Existing categories:")
                        for category in existing_categories[:3]:  # Show first 3 categories
                            parent_info = f" (Parent: {category['parent']})" if category['parent'] != 0 else ""
                            self.logger.info(f"     - {category['name']} (ID: {category['id']}){parent_info}")

                except Exception as e:
                    self.logger.error(f"❌ Get categories failed: {e}")
                    self.test_results['errors'].append(f"Get categories: {e}")

                # Test 2: Create a new parent category
                self.logger.info("➕ Testing create_category (parent)...")
                test_category_name = f"API-Test-Category-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                try:
                    created_category = await client.create_category(
                        name=test_category_name,
                        description="A test category created by the WordPress API test script",
                        slug=f"api-test-category-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                    )

                    category_results['create_category'] = True
                    category_results['created_category_id'] = created_category['id']
                    self.logger.info(f"✅ Parent category created successfully!")
                    self.logger.info(f"   Category ID: {created_category['id']}")
                    self.logger.info(f"   Category Name: {created_category['name']}")
                    self.logger.info(f"   Category Slug: {created_category['slug']}")

                except Exception as e:
                    self.logger.error(f"❌ Create category failed: {e}")
                    self.test_results['errors'].append(f"Create category: {e}")

                # Test 3: Create a child category (if parent creation was successful)
                if category_results['created_category_id']:
                    self.logger.info("👶 Testing create_category (child)...")
                    child_category_name = f"API-Test-Child-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                    try:
                        created_child_category = await client.create_category(
                            name=child_category_name,
                            description="A test child category created by the WordPress API test script",
                            slug=f"api-test-child-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
                            parent=category_results['created_category_id']
                        )

                        category_results['create_child_category'] = True
                        category_results['created_child_category_id'] = created_child_category['id']
                        self.logger.info(f"✅ Child category created successfully!")
                        self.logger.info(f"   Child Category ID: {created_child_category['id']}")
                        self.logger.info(f"   Child Category Name: {created_child_category['name']}")
                        self.logger.info(f"   Parent Category ID: {created_child_category['parent']}")

                    except Exception as e:
                        self.logger.error(f"❌ Create child category failed: {e}")
                        self.test_results['errors'].append(f"Create child category: {e}")
                else:
                    self.logger.warning("⚠️ Skipping child category creation (no parent category created)")

                # Test 4: Update the created category (if creation was successful)
                if category_results['created_category_id']:
                    self.logger.info("✏️ Testing update_category...")
                    try:
                        updated_category = await client.update_category(
                            category_id=category_results['created_category_id'],
                            name=f"{test_category_name} (Updated)",
                            description="Updated description for the test category"
                        )

                        category_results['update_category'] = True
                        self.logger.info(f"✅ Category updated successfully!")
                        self.logger.info(f"   Updated Name: {updated_category['name']}")
                        self.logger.info(f"   Updated Description: {updated_category['description']}")

                    except Exception as e:
                        self.logger.error(f"❌ Update category failed: {e}")
                        self.test_results['errors'].append(f"Update category: {e}")
                else:
                    self.logger.warning("⚠️ Skipping category update (no category created)")

                # Test 5: Delete the created categories (cleanup)
                # Delete child category first (if it exists)
                if category_results['created_child_category_id']:
                    self.logger.info("🗑️ Testing delete_category (child)...")
                    try:
                        await client.delete_category(
                            category_id=category_results['created_child_category_id'],
                            force=True
                        )
                        self.logger.info(f"✅ Child category deleted successfully!")
                        self.logger.info(f"   Deleted Child Category ID: {category_results['created_child_category_id']}")

                    except Exception as e:
                        self.logger.error(f"❌ Delete child category failed: {e}")
                        self.test_results['errors'].append(f"Delete child category: {e}")

                # Delete parent category
                if category_results['created_category_id']:
                    self.logger.info("🗑️ Testing delete_category (parent)...")
                    try:
                        deleted_category = await client.delete_category(
                            category_id=category_results['created_category_id'],
                            force=True
                        )

                        category_results['delete_category'] = True
                        self.logger.info(f"✅ Parent category deleted successfully!")
                        self.logger.info(f"   Deleted Category ID: {category_results['created_category_id']}")

                    except Exception as e:
                        self.logger.error(f"❌ Delete category failed: {e}")
                        self.test_results['errors'].append(f"Delete category: {e}")
                else:
                    self.logger.warning("⚠️ Skipping category deletion (no category created)")

        except Exception as e:
            self.logger.error(f"❌ Unexpected error during category management: {e}")
            self.test_results['errors'].append(f"Category management unexpected: {e}")

        # Update main test results
        self.test_results['category_management'] = category_results

        # Log category management summary
        passed_category_tests = sum(1 for key in ['get_categories', 'create_category', 'update_category', 'delete_category', 'create_child_category']
                                  if category_results[key])
        total_category_tests = 5

        self.logger.info(f"📁 Category Management Summary: {passed_category_tests}/{total_category_tests} tests passed")

        return category_results

    async def test_category_integration(self) -> Dict[str, Any]:
        """Test category integration with post creation and publishing workflow."""
        self.logger.info("🔗 Testing Category Integration...")

        integration_results = {
            'post_with_category': False,
            'category_assignment_verification': False,
            'published_post_category_retention': False,
            'created_category_id': None,
            'created_post_id': None
        }

        try:
            async with WordPressClient(region_code='test_admin') as client:

                # Step 1: Create a test category for integration testing
                self.logger.info("📁 Creating test category for integration...")
                test_category_name = f"Integration-Test-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                try:
                    test_category = await client.create_category(
                        name=test_category_name,
                        description="Category created for integration testing",
                        slug=f"integration-test-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
                    )

                    integration_results['created_category_id'] = test_category['id']
                    self.logger.info(f"✅ Test category created: {test_category['name']} (ID: {test_category['id']})")

                except Exception as e:
                    self.logger.error(f"❌ Failed to create test category: {e}")
                    self.test_results['errors'].append(f"Integration test category creation: {e}")
                    return integration_results

                # Step 2: Create a draft post with the test category assigned
                self.logger.info("📝 Creating draft post with category assignment...")
                try:
                    # Try to add a featured image to the integration test post
                    featured_media_id = None
                    test_images_dir = Path(__file__).parent.parent / "utils" / "wordpress" / "example" / "test_images"

                    if test_images_dir.exists():
                        try:
                            self.logger.info("🖼️ Adding featured image to category integration post...")
                            media_handler = client.get_media_handler()

                            # Use a different image for integration test
                            featured_image_file = test_images_dir / "special_square_jpeg.jpg"
                            if not featured_image_file.exists():
                                featured_image_file = test_images_dir / "test_large_text_jpeg.jpg"

                            if featured_image_file.exists():
                                featured_media = await media_handler.upload_from_file(
                                    file_path=featured_image_file,
                                    title=f"Featured Image - Category Integration Test ({test_category_name})",
                                    alt_text=f"Featured image for category integration test with {test_category_name}",
                                    caption=f"This image demonstrates category integration with featured images"
                                )
                                featured_media_id = featured_media['id']
                                self.logger.info(f"✅ Featured image uploaded for integration post: {featured_media_id}")

                        except Exception as e:
                            self.logger.warning(f"⚠️ Failed to add featured image to integration post: {e}")
                            # Continue without featured image

                    post_data = {
                        'title': f'Integration Test Post - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                        'content': f'''
                        <h2>Category Integration Test Post</h2>
                        <p>This post was created to test category assignment and retention through the publishing workflow.</p>

                        <h3>Test Category</h3>
                        <p>This post is assigned to the category: <strong>{test_category_name}</strong></p>

                        <h3>Integration Tests</h3>
                        <ul>
                            <li>✅ Category assignment during post creation</li>
                            <li>✅ Category verification in draft post</li>
                            <li>✅ Category retention after publishing</li>
                            <li>✅ Featured image assignment and retention</li>
                        </ul>

                        <p><strong>Test completed at:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                        ''',
                        'status': 'draft',
                        'categories': [integration_results['created_category_id']],  # Use category ID
                        'excerpt': f'Integration test post for category {test_category_name}'
                    }

                    # Add featured image if available
                    if featured_media_id:
                        post_data['featured_media'] = featured_media_id

                    created_post = await client.create_post(**post_data)

                    integration_results['post_with_category'] = True
                    integration_results['created_post_id'] = created_post['id']
                    self.logger.info(f"✅ Draft post created with category assignment!")
                    self.logger.info(f"   Post ID: {created_post['id']}")
                    self.logger.info(f"   Post Title: {created_post['title']['rendered']}")
                    self.logger.info(f"   Assigned Categories: {created_post['categories']}")

                except Exception as e:
                    self.logger.error(f"❌ Failed to create post with category: {e}")
                    self.test_results['errors'].append(f"Post with category creation: {e}")

                # Step 3: Verify category assignment in the created post
                if integration_results['created_post_id']:
                    self.logger.info("🔍 Verifying category assignment in draft post...")
                    try:
                        # Get the created post directly to verify category assignment
                        # Use get_posts with status filter to include draft posts
                        posts = await client.get_posts(
                            per_page=10,
                            status='draft',  # Include draft posts
                            categories=[integration_results['created_category_id']]
                        )

                        # Check if our post is in the results
                        post_found = any(post['id'] == integration_results['created_post_id'] for post in posts)

                        if post_found:
                            integration_results['category_assignment_verification'] = True
                            self.logger.info(f"✅ Category assignment verified in draft post!")
                            self.logger.info(f"   Post found in category {integration_results['created_category_id']} filter")
                        else:
                            # Alternative verification: get the specific post and check its categories
                            self.logger.info("   Trying alternative verification method...")
                            specific_post = await client.get_post(integration_results['created_post_id'])
                            if integration_results['created_category_id'] in specific_post['categories']:
                                integration_results['category_assignment_verification'] = True
                                self.logger.info(f"✅ Category assignment verified via direct post lookup!")
                                self.logger.info(f"   Post categories: {specific_post['categories']}")
                            else:
                                self.logger.error(f"❌ Category not found in post categories: {specific_post['categories']}")
                                self.test_results['errors'].append("Category assignment verification failed")

                    except Exception as e:
                        self.logger.error(f"❌ Failed to verify category assignment: {e}")
                        self.test_results['errors'].append(f"Category assignment verification: {e}")

                # Step 4: Publish the post and verify category retention
                if integration_results['created_post_id']:
                    self.logger.info("📢 Publishing post and verifying category retention...")
                    try:
                        # Publish the post
                        published_post = await client.update_post(
                            post_id=integration_results['created_post_id'],
                            status='publish'
                        )

                        self.logger.info(f"✅ Post published successfully!")
                        self.logger.info(f"   Published Post ID: {published_post['id']}")
                        self.logger.info(f"   Published Status: {published_post['status']}")
                        self.logger.info(f"   Categories after publishing: {published_post['categories']}")

                        # Verify category retention after publishing
                        if integration_results['created_category_id'] in published_post['categories']:
                            integration_results['published_post_category_retention'] = True
                            self.logger.info(f"✅ Category retention verified after publishing!")
                        else:
                            self.logger.error(f"❌ Category lost after publishing")
                            self.test_results['errors'].append("Category retention failed after publishing")

                    except Exception as e:
                        self.logger.error(f"❌ Failed to publish post or verify category retention: {e}")
                        self.test_results['errors'].append(f"Post publishing with category retention: {e}")

                # Cleanup: Delete the test post and category
                if integration_results['created_post_id']:
                    try:
                        await client.delete_post(integration_results['created_post_id'], force=True)
                        self.logger.info(f"🧹 Cleaned up test post: {integration_results['created_post_id']}")
                    except Exception as e:
                        self.logger.warning(f"⚠️ Failed to cleanup test post: {e}")

                if integration_results['created_category_id']:
                    try:
                        await client.delete_category(integration_results['created_category_id'], force=True)
                        self.logger.info(f"🧹 Cleaned up test category: {integration_results['created_category_id']}")
                    except Exception as e:
                        self.logger.warning(f"⚠️ Failed to cleanup test category: {e}")

        except Exception as e:
            self.logger.error(f"❌ Unexpected error during category integration testing: {e}")
            self.test_results['errors'].append(f"Category integration unexpected: {e}")

        # Update main test results
        self.test_results['category_integration'] = integration_results

        # Log integration test summary
        passed_integration_tests = sum(1 for key in ['post_with_category', 'category_assignment_verification', 'published_post_category_retention']
                                     if integration_results[key])
        total_integration_tests = 3

        self.logger.info(f"🔗 Category Integration Summary: {passed_integration_tests}/{total_integration_tests} tests passed")

        return integration_results

    async def test_media_upload(self) -> Dict[str, Any]:
        """Test comprehensive media upload functionality."""
        self.logger.info("🖼️ Testing Media Upload...")

        media_results = {
            'upload_jpeg': False,
            'upload_png': False,
            'upload_webp': False,
            'metadata_verification': False,
            'file_size_validation': False,
            'format_validation': False,
            'batch_upload': False,
            'uploaded_media_ids': [],
            'upload_errors': []
        }

        # Get test images directory
        test_images_dir = Path(__file__).parent.parent / "utils" / "wordpress" / "example" / "test_images"

        if not test_images_dir.exists():
            self.logger.error(f"❌ Test images directory not found: {test_images_dir}")
            self.test_results['errors'].append("Media upload: Test images directory not found")
            return media_results

        try:
            async with WordPressClient(region_code='test_admin') as client:
                media_handler = client.get_media_handler()

                # Test 1: Upload JPEG image
                self.logger.info("📸 Testing JPEG upload...")
                try:
                    jpeg_file = test_images_dir / "test_medium_gradient_jpeg.jpg"
                    if jpeg_file.exists():
                        jpeg_media = await media_handler.upload_from_file(
                            file_path=jpeg_file,
                            title="Test JPEG Image - Media Upload Test",
                            alt_text="A test JPEG image for WordPress media upload testing",
                            caption="Test JPEG uploaded via WordPress REST API",
                            description="This JPEG image tests the media upload functionality"
                        )

                        media_results['upload_jpeg'] = True
                        media_results['uploaded_media_ids'].append(jpeg_media['id'])
                        self.logger.info(f"✅ JPEG upload successful!")
                        self.logger.info(f"   Media ID: {jpeg_media['id']}")
                        self.logger.info(f"   URL: {jpeg_media['source_url']}")
                        self.logger.info(f"   Title: {jpeg_media['title']['rendered']}")

                        # Verify metadata
                        if (jpeg_media.get('alt_text') and
                            jpeg_media.get('caption', {}).get('rendered') and
                            jpeg_media.get('description', {}).get('rendered')):
                            media_results['metadata_verification'] = True
                            self.logger.info(f"✅ JPEG metadata verification passed")

                    else:
                        self.logger.warning(f"⚠️ JPEG test file not found: {jpeg_file}")

                except Exception as e:
                    self.logger.error(f"❌ JPEG upload failed: {e}")
                    media_results['upload_errors'].append(f"JPEG upload: {e}")
                    self.test_results['errors'].append(f"JPEG upload: {e}")

                # Test 2: Upload PNG image
                self.logger.info("🖼️ Testing PNG upload...")
                try:
                    png_file = test_images_dir / "test_medium_text_png.png"
                    if png_file.exists():
                        png_media = await media_handler.upload_from_file(
                            file_path=png_file,
                            title="Test PNG Image - Media Upload Test",
                            alt_text="A test PNG image for WordPress media upload testing",
                            caption="Test PNG uploaded via WordPress REST API"
                        )

                        media_results['upload_png'] = True
                        media_results['uploaded_media_ids'].append(png_media['id'])
                        self.logger.info(f"✅ PNG upload successful!")
                        self.logger.info(f"   Media ID: {png_media['id']}")
                        self.logger.info(f"   URL: {png_media['source_url']}")

                    else:
                        self.logger.warning(f"⚠️ PNG test file not found: {png_file}")

                except Exception as e:
                    self.logger.error(f"❌ PNG upload failed: {e}")
                    media_results['upload_errors'].append(f"PNG upload: {e}")
                    self.test_results['errors'].append(f"PNG upload: {e}")

                # Test 3: Upload WebP image (if supported)
                self.logger.info("🌐 Testing WebP upload...")
                try:
                    webp_file = test_images_dir / "test_medium_solid_webp.webp"
                    if webp_file.exists():
                        webp_media = await media_handler.upload_from_file(
                            file_path=webp_file,
                            title="Test WebP Image - Media Upload Test",
                            alt_text="A test WebP image for WordPress media upload testing"
                        )

                        media_results['upload_webp'] = True
                        media_results['uploaded_media_ids'].append(webp_media['id'])
                        self.logger.info(f"✅ WebP upload successful!")
                        self.logger.info(f"   Media ID: {webp_media['id']}")
                        self.logger.info(f"   URL: {webp_media['source_url']}")

                    else:
                        self.logger.warning(f"⚠️ WebP test file not found: {webp_file}")

                except Exception as e:
                    self.logger.warning(f"⚠️ WebP upload failed (may not be supported): {e}")
                    # WebP might not be supported, so don't count as error
                    media_results['upload_errors'].append(f"WebP upload: {e}")

                # Test 4: File size validation
                self.logger.info("📏 Testing file size validation...")
                try:
                    large_file = test_images_dir / "test_extra_large_text_jpeg.jpg"
                    if large_file.exists():
                        file_size = large_file.stat().st_size
                        self.logger.info(f"   Testing with file size: {file_size / (1024*1024):.2f}MB")

                        # This should succeed for reasonable file sizes
                        size_test_media = await media_handler.upload_from_file(
                            file_path=large_file,
                            title="File Size Test Image"
                        )

                        media_results['file_size_validation'] = True
                        media_results['uploaded_media_ids'].append(size_test_media['id'])
                        self.logger.info(f"✅ File size validation passed")

                except Exception as e:
                    self.logger.error(f"❌ File size validation failed: {e}")
                    media_results['upload_errors'].append(f"File size validation: {e}")

                # Test 5: Format validation
                self.logger.info("🔍 Testing format validation...")
                try:
                    # Test with different formats to ensure they're properly handled
                    format_test_files = [
                        ("test_small_gradient_jpeg.jpg", "JPEG"),
                        ("test_small_solid_png.png", "PNG")
                    ]

                    format_success_count = 0
                    for filename, format_name in format_test_files:
                        test_file = test_images_dir / filename
                        if test_file.exists():
                            format_media = await media_handler.upload_from_file(
                                file_path=test_file,
                                title=f"Format Test - {format_name}"
                            )
                            format_success_count += 1
                            media_results['uploaded_media_ids'].append(format_media['id'])
                            self.logger.info(f"   ✅ {format_name} format validation passed")

                    if format_success_count > 0:
                        media_results['format_validation'] = True
                        self.logger.info(f"✅ Format validation passed ({format_success_count} formats tested)")

                except Exception as e:
                    self.logger.error(f"❌ Format validation failed: {e}")
                    media_results['upload_errors'].append(f"Format validation: {e}")

                # Test 6: Batch upload (if supported)
                self.logger.info("📦 Testing batch upload...")
                try:
                    batch_files = []
                    batch_test_files = [
                        "test_thumbnail_gradient_jpeg.jpg",
                        "test_thumbnail_text_png.png",
                        "test_thumbnail_solid_jpeg.jpg"
                    ]

                    for filename in batch_test_files:
                        file_path = test_images_dir / filename
                        if file_path.exists():
                            batch_files.append(file_path)

                    if len(batch_files) >= 2:
                        # Upload files individually but track as batch test
                        batch_success_count = 0
                        for file_path in batch_files[:3]:  # Limit to 3 files
                            try:
                                batch_media = await media_handler.upload_from_file(
                                    file_path=file_path,
                                    title=f"Batch Test - {file_path.name}"
                                )
                                batch_success_count += 1
                                media_results['uploaded_media_ids'].append(batch_media['id'])
                            except Exception as batch_e:
                                self.logger.warning(f"   ⚠️ Batch item failed: {batch_e}")

                        if batch_success_count >= 2:
                            media_results['batch_upload'] = True
                            self.logger.info(f"✅ Batch upload test passed ({batch_success_count} files)")

                except Exception as e:
                    self.logger.error(f"❌ Batch upload test failed: {e}")
                    media_results['upload_errors'].append(f"Batch upload: {e}")

        except Exception as e:
            self.logger.error(f"❌ Unexpected error during media upload testing: {e}")
            self.test_results['errors'].append(f"Media upload unexpected: {e}")

        # Update main test results
        self.test_results['media_upload'] = media_results

        # Log media upload summary
        passed_media_tests = sum(1 for key in ['upload_jpeg', 'upload_png', 'upload_webp', 'metadata_verification', 'file_size_validation', 'format_validation', 'batch_upload']
                               if media_results[key])
        total_media_tests = 7

        self.logger.info(f"🖼️ Media Upload Summary: {passed_media_tests}/{total_media_tests} tests passed")
        self.logger.info(f"   Uploaded {len(media_results['uploaded_media_ids'])} media files")

        if media_results['upload_errors']:
            self.logger.info(f"   Upload errors: {len(media_results['upload_errors'])}")

        return media_results

    async def test_featured_image(self) -> Dict[str, Any]:
        """Test featured image assignment and verification functionality."""
        self.logger.info("🌟 Testing Featured Image Assignment...")

        featured_results = {
            'assignment_during_creation': False,
            'assignment_after_creation': False,
            'retention_through_publish': False,
            'verification_in_response': False,
            'url_accessibility': False,
            'created_post_id': None,
            'featured_media_id': None,
            'featured_image_url': None
        }

        # Get test images directory
        test_images_dir = Path(__file__).parent.parent / "utils" / "wordpress" / "example" / "test_images"

        if not test_images_dir.exists():
            self.logger.error(f"❌ Test images directory not found: {test_images_dir}")
            self.test_results['errors'].append("Featured image: Test images directory not found")
            return featured_results

        try:
            async with WordPressClient(region_code='test_admin') as client:
                media_handler = client.get_media_handler()

                # Step 1: Upload a featured image
                self.logger.info("📤 Uploading featured image...")
                try:
                    featured_image_file = test_images_dir / "special_banner_jpeg.jpg"
                    if not featured_image_file.exists():
                        # Fallback to another image
                        featured_image_file = test_images_dir / "test_large_gradient_jpeg.jpg"

                    if featured_image_file.exists():
                        featured_media = await media_handler.upload_from_file(
                            file_path=featured_image_file,
                            title="Featured Image Test - WordPress API",
                            alt_text="Featured image for testing WordPress REST API functionality",
                            caption="This image tests featured image assignment and verification"
                        )

                        featured_results['featured_media_id'] = featured_media['id']
                        featured_results['featured_image_url'] = featured_media['source_url']
                        self.logger.info(f"✅ Featured image uploaded successfully!")
                        self.logger.info(f"   Media ID: {featured_media['id']}")
                        self.logger.info(f"   URL: {featured_media['source_url']}")

                    else:
                        self.logger.error("❌ No suitable featured image file found")
                        return featured_results

                except Exception as e:
                    self.logger.error(f"❌ Featured image upload failed: {e}")
                    self.test_results['errors'].append(f"Featured image upload: {e}")
                    return featured_results

                # Step 2: Test assignment during post creation
                self.logger.info("📝 Testing featured image assignment during post creation...")
                try:
                    post_with_featured = await client.create_post(
                        title=f"Featured Image Test Post - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        content="""
                        <h2>Featured Image Test Post</h2>
                        <p>This post was created to test featured image assignment functionality.</p>

                        <h3>Test Features</h3>
                        <ul>
                            <li>✅ Featured image assignment during creation</li>
                            <li>✅ Featured image verification in response</li>
                            <li>✅ Featured image retention through publish workflow</li>
                            <li>✅ Featured image URL accessibility</li>
                        </ul>

                        <p><strong>Test completed at:</strong> {}</p>
                        """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                        status='draft',
                        featured_media=featured_results['featured_media_id'],
                        excerpt='A test post for featured image functionality testing'
                    )

                    featured_results['assignment_during_creation'] = True
                    featured_results['created_post_id'] = post_with_featured['id']
                    self.logger.info(f"✅ Post created with featured image during creation!")
                    self.logger.info(f"   Post ID: {post_with_featured['id']}")
                    self.logger.info(f"   Featured Media ID: {post_with_featured.get('featured_media', 'Not found')}")

                    # Verify featured image in response
                    if post_with_featured.get('featured_media') == featured_results['featured_media_id']:
                        featured_results['verification_in_response'] = True
                        self.logger.info(f"✅ Featured image verification in response passed!")

                except Exception as e:
                    self.logger.error(f"❌ Post creation with featured image failed: {e}")
                    self.test_results['errors'].append(f"Featured image during creation: {e}")

                # Step 3: Test assignment after post creation (if step 2 failed)
                if not featured_results['assignment_during_creation']:
                    self.logger.info("📎 Testing featured image assignment after post creation...")
                    try:
                        # Create post without featured image first
                        post_without_featured = await client.create_post(
                            title=f"Post for Featured Image Assignment - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                            content="<p>This post will have a featured image assigned after creation.</p>",
                            status='draft'
                        )

                        # Then assign featured image
                        updated_post = await media_handler.set_featured_image(
                            post_id=post_without_featured['id'],
                            media_id=featured_results['featured_media_id']
                        )

                        featured_results['assignment_after_creation'] = True
                        featured_results['created_post_id'] = updated_post['id']
                        self.logger.info(f"✅ Featured image assigned after post creation!")
                        self.logger.info(f"   Post ID: {updated_post['id']}")
                        self.logger.info(f"   Featured Media ID: {updated_post.get('featured_media')}")

                        if updated_post.get('featured_media') == featured_results['featured_media_id']:
                            featured_results['verification_in_response'] = True
                            self.logger.info(f"✅ Featured image verification after assignment passed!")

                    except Exception as e:
                        self.logger.error(f"❌ Featured image assignment after creation failed: {e}")
                        self.test_results['errors'].append(f"Featured image after creation: {e}")

                # Step 4: Test retention through publish workflow
                if featured_results['created_post_id']:
                    self.logger.info("📢 Testing featured image retention through publish workflow...")
                    try:
                        # Publish the post
                        published_post = await client.update_post(
                            post_id=featured_results['created_post_id'],
                            status='publish'
                        )

                        self.logger.info(f"✅ Post published successfully!")
                        self.logger.info(f"   Published Post ID: {published_post['id']}")
                        self.logger.info(f"   Featured Media after publish: {published_post.get('featured_media')}")

                        # Verify featured image retention
                        if published_post.get('featured_media') == featured_results['featured_media_id']:
                            featured_results['retention_through_publish'] = True
                            self.logger.info(f"✅ Featured image retention through publish verified!")
                        else:
                            self.logger.error(f"❌ Featured image lost during publishing")

                    except Exception as e:
                        self.logger.error(f"❌ Featured image retention test failed: {e}")
                        self.test_results['errors'].append(f"Featured image retention: {e}")

                # Step 5: Test URL accessibility
                if featured_results['featured_image_url']:
                    self.logger.info("🌐 Testing featured image URL accessibility...")
                    try:
                        import aiohttp
                        async with aiohttp.ClientSession() as session:
                            async with session.head(featured_results['featured_image_url']) as response:
                                if response.status == 200:
                                    featured_results['url_accessibility'] = True
                                    self.logger.info(f"✅ Featured image URL is accessible!")
                                    self.logger.info(f"   URL: {featured_results['featured_image_url']}")
                                    self.logger.info(f"   Status: {response.status}")
                                else:
                                    self.logger.warning(f"⚠️ Featured image URL returned status: {response.status}")

                    except Exception as e:
                        self.logger.warning(f"⚠️ Featured image URL accessibility test failed: {e}")
                        # Don't count as critical error since it might be network-related

                # Cleanup: Delete test post and media (optional - comment out to keep for manual verification)
                # if featured_results['created_post_id']:
                #     try:
                #         await client.delete_post(featured_results['created_post_id'], force=True)
                #         self.logger.info(f"🧹 Cleaned up test post: {featured_results['created_post_id']}")
                #     except Exception as e:
                #         self.logger.warning(f"⚠️ Failed to cleanup test post: {e}")

                # if featured_results['featured_media_id']:
                #     try:
                #         await client.delete_media(featured_results['featured_media_id'], force=True)
                #         self.logger.info(f"🧹 Cleaned up featured image: {featured_results['featured_media_id']}")
                #     except Exception as e:
                #         self.logger.warning(f"⚠️ Failed to cleanup featured image: {e}")

        except Exception as e:
            self.logger.error(f"❌ Unexpected error during featured image testing: {e}")
            self.test_results['errors'].append(f"Featured image unexpected: {e}")

        # Update main test results
        self.test_results['featured_image'] = featured_results

        # Log featured image summary
        passed_featured_tests = sum(1 for key in ['assignment_during_creation', 'assignment_after_creation', 'retention_through_publish', 'verification_in_response', 'url_accessibility']
                                  if featured_results[key])
        total_featured_tests = 5

        self.logger.info(f"🌟 Featured Image Summary: {passed_featured_tests}/{total_featured_tests} tests passed")

        return featured_results

    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run the complete test suite."""
        self.logger.info("🚀 Starting Comprehensive WordPress API Test Suite")
        self.logger.info("=" * 60)

        # Test 1: Authentication
        auth_success = await self.test_authentication()
        if not auth_success:
            self.logger.error("❌ Authentication tests failed. Stopping test suite.")
            return self.test_results

        # Test 2: Post Creation
        created_post = await self.test_post_creation()
        if not created_post:
            self.logger.error("❌ Post creation failed. Continuing with other tests to gather more information.")
            # Continue with tests using existing post
            try:
                async with WordPressClient(region_code='test') as client:
                    posts = await client.get_posts(per_page=1)
                    if posts:
                        created_post = posts[0]  # Use existing post for testing
                        post_id = created_post['id']
                        self.logger.info(f"ℹ️ Using existing post {post_id} for remaining tests")
                    else:
                        self.logger.error("❌ No existing posts found. Cannot continue with tests.")
                        return self.test_results
            except Exception as e:
                self.logger.error(f"❌ Failed to get existing posts: {e}")
                return self.test_results
        else:
            post_id = created_post['id']

        # Test 3: Post Moderation (Admin) - Do this before comments
        if created_post and self.test_results['post_creation']:
            post_mod_success = await self.test_post_moderation(post_id)
            if not post_mod_success:
                self.logger.error("❌ Post moderation failed.")
        else:
            self.logger.warning("⚠️ Skipping post moderation (no post created)")

        # Test 4: Comment Creation (after post is published)
        created_comment = await self.test_comment_creation(post_id)
        if not created_comment:
            self.logger.error("❌ Comment creation failed. Continuing with comment moderation test.")

        comment_id = created_comment['id'] if created_comment else None

        # Test 5: Comment Moderation (Admin)
        if comment_id:
            comment_mod_success = await self.test_comment_moderation(comment_id)
            if not comment_mod_success:
                self.logger.error("❌ Comment moderation failed.")
        else:
            self.logger.warning("⚠️ Skipping comment moderation (no comment created)")

        # Test 6: Tag Management
        tag_results = await self.test_tag_management()
        if not any(tag_results.values()):
            self.logger.error("❌ All tag management tests failed.")

        # Test 7: Category Management
        category_results = await self.test_category_management()
        if not any(category_results.values()):
            self.logger.error("❌ All category management tests failed.")

        # Test 8: Category Integration
        integration_results = await self.test_category_integration()
        if not any(integration_results.values()):
            self.logger.error("❌ All category integration tests failed.")

        # Test 9: Media Upload
        media_results = await self.test_media_upload()
        if not any(media_results.values()):
            self.logger.error("❌ All media upload tests failed.")

        # Test 10: Featured Image
        featured_results = await self.test_featured_image()
        if not any(featured_results.values()):
            self.logger.error("❌ All featured image tests failed.")

        return self.test_results

    def print_test_summary(self):
        """Print a comprehensive test summary."""
        self.logger.info("\n" + "=" * 60)
        self.logger.info("📊 TEST SUMMARY")
        self.logger.info("=" * 60)

        # Authentication results
        auth_results = self.test_results['authentication']
        self.logger.info(f"🔐 Authentication:")
        self.logger.info(f"   Test User (chieh): {'✅ PASS' if auth_results['test_user'] else '❌ FAIL'}")
        self.logger.info(f"   Admin User (admin): {'✅ PASS' if auth_results['admin_user'] else '❌ FAIL'}")

        # Operation results
        operations = [
            ('📝 Post Creation', 'post_creation'),
            ('💬 Comment Creation', 'comment_creation'),
            ('🔍 Post Moderation', 'post_moderation'),
            ('✅ Comment Moderation', 'comment_moderation')
        ]

        for name, key in operations:
            status = '✅ PASS' if self.test_results[key] else '❌ FAIL'
            self.logger.info(f"{name}: {status}")

        # Tag management results
        tag_results = self.test_results.get('tag_management', {})
        if tag_results:
            self.logger.info(f"🏷️ Tag Management:")
            tag_operations = [
                ('   Get Tags', 'get_tags'),
                ('   Create Tag', 'create_tag'),
                ('   Update Tag', 'update_tag'),
                ('   Delete Tag', 'delete_tag')
            ]

            for name, key in tag_operations:
                status = '✅ PASS' if tag_results.get(key, False) else '❌ FAIL'
                self.logger.info(f"{name}: {status}")

        # Category management results
        category_results = self.test_results.get('category_management', {})
        if category_results:
            self.logger.info(f"📁 Category Management:")
            category_operations = [
                ('   Get Categories', 'get_categories'),
                ('   Create Category', 'create_category'),
                ('   Update Category', 'update_category'),
                ('   Delete Category', 'delete_category'),
                ('   Create Child Category', 'create_child_category')
            ]

            for name, key in category_operations:
                status = '✅ PASS' if category_results.get(key, False) else '❌ FAIL'
                self.logger.info(f"{name}: {status}")

        # Category integration results
        integration_results = self.test_results.get('category_integration', {})
        if integration_results:
            self.logger.info(f"🔗 Category Integration:")
            integration_operations = [
                ('   Post with Category', 'post_with_category'),
                ('   Category Assignment Verification', 'category_assignment_verification'),
                ('   Published Post Category Retention', 'published_post_category_retention')
            ]

            for name, key in integration_operations:
                status = '✅ PASS' if integration_results.get(key, False) else '❌ FAIL'
                self.logger.info(f"{name}: {status}")

        # Media upload results
        media_results = self.test_results.get('media_upload', {})
        if media_results:
            self.logger.info(f"🖼️ Media Upload:")
            media_operations = [
                ('   JPEG Upload', 'upload_jpeg'),
                ('   PNG Upload', 'upload_png'),
                ('   WebP Upload', 'upload_webp'),
                ('   Metadata Verification', 'metadata_verification'),
                ('   File Size Validation', 'file_size_validation'),
                ('   Format Validation', 'format_validation'),
                ('   Batch Upload', 'batch_upload')
            ]

            for name, key in media_operations:
                status = '✅ PASS' if media_results.get(key, False) else '❌ FAIL'
                self.logger.info(f"{name}: {status}")

            # Show upload statistics
            uploaded_count = len(media_results.get('uploaded_media_ids', []))
            error_count = len(media_results.get('upload_errors', []))
            self.logger.info(f"   📊 Uploaded: {uploaded_count} files, Errors: {error_count}")

        # Featured image results
        featured_results = self.test_results.get('featured_image', {})
        if featured_results:
            self.logger.info(f"🌟 Featured Image:")
            featured_operations = [
                ('   Assignment During Creation', 'assignment_during_creation'),
                ('   Assignment After Creation', 'assignment_after_creation'),
                ('   Retention Through Publish', 'retention_through_publish'),
                ('   Verification in Response', 'verification_in_response'),
                ('   URL Accessibility', 'url_accessibility')
            ]

            for name, key in featured_operations:
                status = '✅ PASS' if featured_results.get(key, False) else '❌ FAIL'
                self.logger.info(f"{name}: {status}")

        # Error summary
        if self.test_results['errors']:
            self.logger.info(f"\n❌ Errors encountered ({len(self.test_results['errors'])}):")
            for i, error in enumerate(self.test_results['errors'], 1):
                self.logger.info(f"   {i}. {error}")

            # Check for authentication-related errors and provide guidance
            auth_errors = [error for error in self.test_results['errors'] if 'authentication' in error.lower() or 'not allowed' in error.lower()]
            if auth_errors:
                self.logger.info(f"\n💡 AUTHENTICATION TROUBLESHOOTING:")
                self.logger.info(f"   The WordPress REST API requires proper authentication for write operations.")
                self.logger.info(f"   To fix authentication issues:")
                self.logger.info(f"   1. Enable Application Passwords in WordPress (Users → Profile → Application Passwords)")
                self.logger.info(f"   2. Generate an application password for the test users")
                self.logger.info(f"   3. Update config.yaml to use 'app_password' auth method")
                self.logger.info(f"   4. Or install a REST API authentication plugin")
                self.logger.info(f"   5. Ensure users have 'edit_posts' capability")
        else:
            self.logger.info("\n✅ No errors encountered!")

        # Overall result
        total_tests = 30  # 2 auth + 4 operations + 4 tag + 5 category + 3 integration + 7 media + 5 featured
        passed_tests = (
            sum(auth_results.values()) +
            sum(1 for key in ['post_creation', 'comment_creation', 'post_moderation', 'comment_moderation']
                if self.test_results[key]) +
            sum(1 for key in ['get_tags', 'create_tag', 'update_tag', 'delete_tag']
                if self.test_results.get('tag_management', {}).get(key, False)) +
            sum(1 for key in ['get_categories', 'create_category', 'update_category', 'delete_category', 'create_child_category']
                if self.test_results.get('category_management', {}).get(key, False)) +
            sum(1 for key in ['post_with_category', 'category_assignment_verification', 'published_post_category_retention']
                if self.test_results.get('category_integration', {}).get(key, False)) +
            sum(1 for key in ['upload_jpeg', 'upload_png', 'upload_webp', 'metadata_verification', 'file_size_validation', 'format_validation', 'batch_upload']
                if self.test_results.get('media_upload', {}).get(key, False)) +
            sum(1 for key in ['assignment_during_creation', 'assignment_after_creation', 'retention_through_publish', 'verification_in_response', 'url_accessibility']
                if self.test_results.get('featured_image', {}).get(key, False))
        )

        success_rate = (passed_tests / total_tests) * 100
        self.logger.info(f"\n🎯 Overall Success Rate: {passed_tests}/{total_tests} ({success_rate:.1f}%)")

        if success_rate == 100:
            self.logger.info("🎉 ALL TESTS PASSED! WordPress API is working perfectly.")
        elif success_rate >= 80:
            self.logger.info("✅ Most tests passed. WordPress API is mostly functional.")
        elif success_rate >= 50:
            self.logger.info("⚠️ Some tests failed. WordPress API has issues.")
        else:
            self.logger.info("❌ Many tests failed. WordPress API has serious issues.")


async def main():
    """Main test execution function."""
    print("🚀 WordPress REST API Standalone Test Script")
    print("=" * 60)

    # Setup logging
    test_logger = WordPressTestLogger(log_level="DEBUG")
    logger = logging.getLogger(__name__)

    try:
        # Verify configuration
        logger.info("🔧 Verifying configuration...")
        config = get_config()

        # Check if test configurations exist
        wordpress_apis = config.get('wordpress_apis', {})
        if 'test' not in wordpress_apis:
            logger.error("❌ 'test' configuration not found in wordpress_apis")
            return False

        if 'test_admin' not in wordpress_apis:
            logger.error("❌ 'test_admin' configuration not found in wordpress_apis")
            return False

        logger.info("✅ Configuration verified")

        # Run tests
        tester = WordPressAPITester()
        results = await tester.run_comprehensive_test()

        # Print summary
        tester.print_test_summary()

        # Return success status
        auth_success = all(results['authentication'].values())
        operations_success = all(results[key] for key in ['post_creation', 'comment_creation', 'post_moderation', 'comment_moderation'])
        tag_success = all(results.get('tag_management', {}).get(key, False) for key in ['get_tags', 'create_tag', 'update_tag', 'delete_tag'])
        category_success = all(results.get('category_management', {}).get(key, False) for key in ['get_categories', 'create_category', 'update_category', 'delete_category', 'create_child_category'])
        integration_success = all(results.get('category_integration', {}).get(key, False) for key in ['post_with_category', 'category_assignment_verification', 'published_post_category_retention'])
        media_success = any(results.get('media_upload', {}).get(key, False) for key in ['upload_jpeg', 'upload_png', 'upload_webp'])  # At least one format should work
        featured_success = any(results.get('featured_image', {}).get(key, False) for key in ['assignment_during_creation', 'assignment_after_creation'])  # At least one assignment method should work

        return auth_success and operations_success and tag_success and category_success and integration_success and media_success and featured_success

    except ConfigurationError as e:
        logger.error(f"❌ Configuration error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        logger.exception("Full traceback:")
        return False


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())

    # Exit with appropriate code
    exit_code = 0 if success else 1
    print(f"\n🏁 Test completed with exit code: {exit_code}")
    sys.exit(exit_code)
