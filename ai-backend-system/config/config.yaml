---
api_keys: &api_keys
  openai_api_key: &openai_key ********************************************************************************************************************************************************************
  gemini_api_key: &gemini_key AIzaSyDpD7Ma-qb3kdHARnFM3B_3-_GFNZ2d9Z0
  scrape_do_api_key: &scrape_do_key d5ff6dd82bd34eeab61e3360314c3c97d0735089710
  google_search_api_key: &google_search_api_key YOUR_GOOGLE_SEARCH_API_KEY
  google_cse_id: &google_cse_id YOUR_GOOGLE_CSE_ID

google_cloud_service:
  project_id: &gg_project_id wiseandluck250311
  location: &gg_proj_location australia-southeast1

wordpress_apis:
  # Production test environment with Application Passwords
  test:
    api_url: https://localwp:8443/index.php?rest_route=/wp/v2/
    username: chieh
    password: ATXY rckg m0mL 2bBs pqqS QA6H 
    auth_method: app_password  # Using Application Password
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  # Wordpress editor user for content creation with editor app password
  test:
    api_url: https://localwp:8443/index.php?rest_route=/wp/v2/
    username: wpeditor
    password: plKI G6M7 t7I9 AJv2 GSAT Xh1c 
    auth_method: app_password  # Using Application Password
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
   # Admin user for moderation tasks with admin app password
  test_admin:
    api_url: https://localwp:8443/index.php?rest_route=/wp/v2/
    username: admin
    password: xN3e fLf0 ZCjz gDow 70vt JEXu 
    auth_method: app_password
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  au:
    api_url: https://au.example.com/wp-json/wp/v2/
    username: au_api_user
    password: au_api_password
    auth_method: app_password  # Options: basic, jwt, oauth, app_password
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  de:
    api_url: https://de.example.com/wp-json/wp/v2/
    username: de_api_user
    password: de_api_password
    auth_method: basic
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  us:
    api_url: https://us.example.com/wp-json/wp/v2/
    username: us_api_user
    password: us_api_password
    auth_method: basic
    timeout: 30
    max_retries: 3
    retry_delay: 1.0

# WordPress wrapper configuration
wordpress:
  # Rate limiting settings
  rate_limiting:
    requests_per_minute: 60
    requests_per_hour: 1000
    burst_limit: 10

  # Media upload settings
  media:
    max_file_size_mb: 50
    supported_image_formats: [jpg, jpeg, png, gif, webp, svg]
    supported_video_formats: [mp4, avi, mov, wmv, flv, webm]
    supported_audio_formats: [mp3, wav, ogg, m4a]
    supported_document_formats: [pdf, doc, docx, txt, rtf]

    # Image handling method configuration
    image_handling:
      # Default method: 'local_upload' or 'direct_url' or 'cloud_storage'
      default_method: local_upload

      # Local upload settings (existing behavior)
      local_upload:
        enabled: true
        temp_directory: temp_downloads
        cleanup_after_upload: true
        keep_recent_files: 5

      # Direct URL embedding settings
      direct_url:
        enabled: true
        # URL validation settings
        validation:
          check_accessibility: true
          timeout_seconds: 10
          allowed_domains: []  # Empty means all domains allowed
          blocked_domains: []  # Domains to block
          require_https: false
          max_url_length: 2048

        # URL processing settings
        processing:
          add_cache_busting: false
          resize_parameters: false  # Add width/height parameters to URLs
          default_width: 800
          default_height: 600

        # Fallback behavior when URL is inaccessible
        fallback:
          use_placeholder: true
          placeholder_url: "https://via.placeholder.com/800x600/cccccc/666666?text=Image+Not+Available"
          retry_attempts: 3
          retry_delay_seconds: 2

      # Cloud storage integration settings
      cloud_storage:
        enabled: true

        # AWS S3 configuration
        aws_s3:
          enabled: false
          bucket_name: ""
          region: ""
          access_key_id: ""  # Use environment variable AWS_ACCESS_KEY_ID
          secret_access_key: ""  # Use environment variable AWS_SECRET_ACCESS_KEY
          custom_domain: ""  # Optional CloudFront domain
          url_expiry_hours: 24  # For signed URLs

        # Google Cloud Storage configuration
        google_cloud:
          enabled: false
          bucket_name: ""
          project_id: ""
          credentials_path: ""  # Path to service account JSON
          custom_domain: ""
          url_expiry_hours: 24

        # Azure Blob Storage configuration
        azure_blob:
          enabled: false
          account_name: ""
          account_key: ""  # Use environment variable AZURE_STORAGE_KEY
          container_name: ""
          custom_domain: ""
          url_expiry_hours: 24

        # Generic cloud storage settings
        upload_settings:
          auto_generate_filename: true
          filename_prefix: "wp_media_"
          organize_by_date: true  # Create folders by year/month
          allowed_file_types: [jpg, jpeg, png, gif, webp, svg]
          max_file_size_mb: 50

    # Image optimization settings (applies to local uploads)
    image_optimization:
      enabled: true
      max_width: 1920
      max_height: 1080
      quality: 85

  # Post management settings
  posts:
    default_status: draft  # Options: draft, published, private, pending
    default_comment_status: open  # Options: open, closed
    default_ping_status: closed  # Options: open, closed
    auto_excerpt_length: 150

  # Comment management settings
  comments:
    default_status: hold  # Options: approved, hold, spam, trash
    moderation_required: true
    auto_approve_registered_users: false
    max_comment_length: 5000

  # Batch operation settings
  batch:
    max_posts_per_batch: 20
    max_comments_per_batch: 50
    max_media_per_batch: 10

  # Logging settings specific to WordPress operations
  logging:
    log_api_requests: true
    log_api_responses: true  # Enable for testing and debugging
    log_media_uploads: true
    log_authentication: true
    log_rate_limiting: true
    log_error_details: true

supabase:
  supabase_url: https://izxhtzvphcwshrawqkca.supabase.co
  supabase_key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml6eGh0enZwaGN3c2hyYXdxa2NhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxOTM0MTcsImV4cCI6MjA1NDc2OTQxN30.WRpCxo2f9fVXVa9zqNRU5_ImgEBcaZiyQb5SrpAVbnI

pgvector:
  openai_embedding_model_name: text-embedding-3-small
  openai_embedding_vector_dimension: 1536
  gemini_embedding_model_name: text-embedding-004
  gemini_embedding_vector_dimension: 3072

logging:
  log_level: INFO
  log_file_path: logs/ai_backend_system.log
  log_format: "%(asctime)s - %(levelname)s - %(module)s - %(message)s"

rag_knowledge_base:
  vector_db_table_name: knowledge_documents
  embedding_model_name: text-embedding-ada-002
  index_algorithm: hnsw

region_list:
  # 大洋洲
  - au  # 澳大利亚
  - nz  # 新西兰
  
  # 北美洲
  - us  # 美国
  - ca  # 加拿大
  
  # 欧洲
  - uk  # 英国
  - de  # 德国
  - fr  # 法国
  - it  # 意大利
  - es  # 西班牙
  - nl  # 荷兰
  - se  # 瑞典
  
  # 亚洲
  - jp  # 日本
  - kr  # 韩国
  - sg  # 新加坡
  - my  # 马来西亚
  - th  # 泰国
  - vn  # 越南
  - id  # 印度尼西亚
  - ph  # 菲律宾
  - in  # 印度
  
  # 中东
  - ae  # 阿联酋
  - sa  # 沙特阿拉伯
  - tr  # 土耳其
  
  # 南美洲
  - br  # 巴西
  - mx  # 墨西哥
  - ar  # 阿根廷
  - cl  # 智利

db_table_prefix:
  - deals: backend_deals_
  - deals_name_embeddings: backend_deals_name_embeddings_
agents:
  crawler_engineer:
    # 爬虫工程师代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
    extractor_model_name: gemini-2.5-flash-preview-05-20
    extractor_model_api_key: *gemini_key

  content_collection_assistant:
    # 内容收集助手代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
    max_retry_attempts: 3
    collection_mode:
      - deal
      - comment
      - article
      - video
    
  deal_collector:
    # Deal 收集代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
    deal_max_count_pages: 3
    deal_start_page_url_list: # 根据 region_list 中的地区，设置不同的 start_page_url_list
      - au:
        - https://www.ozbargain.com.au/deals?noexpired=1&page=0
        - https://au.camelcamelcamel.com/popular?p=1
      - de:
        - https://www.dealspotr.com/deals?page=0
      - us:
        - https://www.dealspotr.com/us/deals?page=0
      - uk:
        - https://www.dealspotr.com/uk/deals?page=0

  product_collector:
    # 产品信息收集代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
    
  comment_collector:
    # 评论收集代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
    default_comment_limit: 50
    
  article_collector:
    # 文章收集代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
    
  video_collector:
    # 视频收集代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key

  content_reviewer:
    # 内容评审专员代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key

  content_generator_deal:
    # 内容生成专员代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key

  content_generator_comment:
    # 内容生成专员代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key

  content_generator_article:
    # 内容生成专员代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key

  content_operation_manager:
    # 协调员代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
    testing_mode_name: gemini-2.5-flash-preview-05-20
    testing_mode_api_key: *gemini_key

  # search_assistant:
  #   # 搜索助理代理
  #   agent_model_name: gemini-2.5-flash-preview-05-20
  #   agent_model_api_key: *gemini_key
  #   search_url_list:
  #     - https://www.google.com
  #     - https://www.duckduckgo.com

  data_processor:
    # 数据处理专员代理
    agent_model_name: gemini-2.5-flash-preview-05-20
    agent_model_api_key: *gemini_key
