# WordPress External Images - Quick Start Guide

## 🚀 Quick Setup (5 minutes)

### 1. Enable Direct URL Embedding (Fastest Option)

Add to your `config.yaml`:

```yaml
wordpress:
  media:
    image_handling:
      default_method: direct_url
      direct_url:
        enabled: true
        validation:
          check_accessibility: true
          timeout_seconds: 10
```

### 2. Create Your First Post with External Images

```python
import asyncio
from utils.wordpress.wordpress_client import create_wordpress_client

async def quick_example():
    async with create_wordpress_client('test') as client:
        # Define external images
        external_images = [
            {
                'url': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
                'title': 'Mountain Lake',
                'alt_text': 'Beautiful mountain lake reflection',
                'is_featured': True
            }
        ]
        
        # Create post with external images
        post = await client.create_post_with_external_images(
            title="My First External Image Post",
            content="""
            <h2>Welcome to External Image Handling!</h2>
            <p>This post uses external images without local storage.</p>
            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800" 
                 alt="Mountain landscape" style="max-width: 100%;">
            """,
            external_images=external_images,
            image_handling_method='direct_url',
            status='draft'
        )
        
        print(f"✅ Created post: {post['id']}")
        print(f"🔗 URL: {post.get('link', 'N/A')}")

# Run the example
asyncio.run(quick_example())
```

### 3. Test the Setup

```bash
# Run the demo script
python ai-backend-system/utils/wordpress/example/direct_url_image_demo.py

# Or run tests
python ai-backend-system/tests/run_external_image_tests.py
```

## 🎯 Common Use Cases

### Use Case 1: Blog Post with Multiple Images

```python
async def blog_post_example():
    async with create_wordpress_client('test') as client:
        external_images = [
            {
                'url': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200',
                'title': 'Hero Image',
                'alt_text': 'Mountain landscape hero image',
                'is_featured': True
            },
            {
                'url': 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800',
                'title': 'Gallery Image 1',
                'alt_text': 'Forest path in sunlight'
            },
            {
                'url': 'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=600',
                'title': 'Gallery Image 2',
                'alt_text': 'Sunset sky colors'
            }
        ]
        
        content = """
        <h2>Amazing Nature Photography</h2>
        <p>Discover the beauty of nature through these stunning photographs.</p>
        
        <h3>Mountain Landscapes</h3>
        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200" 
             alt="Mountain landscape" style="width: 100%; border-radius: 8px;">
        
        <h3>Forest Paths</h3>
        <img src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800" 
             alt="Forest path" style="width: 100%; border-radius: 8px;">
        
        <h3>Sky Colors</h3>
        <img src="https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=600" 
             alt="Sunset sky" style="width: 100%; border-radius: 8px;">
        """
        
        post = await client.create_post_with_external_images(
            title="Nature Photography Collection",
            content=content,
            external_images=external_images,
            categories=['Photography', 'Nature'],
            tags=['landscape', 'nature', 'photography'],
            status='draft'
        )
        
        print(f"✅ Blog post created: {post['id']}")

asyncio.run(blog_post_example())
```

### Use Case 2: Product Showcase

```python
async def product_showcase():
    async with create_wordpress_client('test') as client:
        # Product images from e-commerce sites
        product_images = [
            {
                'url': 'https://m.media-amazon.com/images/I/41EKhUc7TKL._SY300_.jpg',
                'title': 'Product Main Image',
                'alt_text': 'Product front view',
                'is_featured': True
            }
        ]
        
        content = """
        <h2>Product Review: Amazing Gadget</h2>
        <p>Here's our detailed review of this fantastic product.</p>
        
        <div style="text-align: center;">
            <img src="https://m.media-amazon.com/images/I/41EKhUc7TKL._SY300_.jpg" 
                 alt="Product image" style="max-width: 300px; border: 1px solid #ddd; padding: 10px;">
        </div>
        
        <h3>Key Features</h3>
        <ul>
            <li>High quality materials</li>
            <li>Excellent performance</li>
            <li>Great value for money</li>
        </ul>
        """
        
        post = await client.create_post_with_external_images(
            title="Product Review: Amazing Gadget",
            content=content,
            external_images=product_images,
            categories=['Reviews', 'Products'],
            tags=['review', 'product', 'gadget'],
            status='draft'
        )
        
        print(f"✅ Product post created: {post['id']}")

asyncio.run(product_showcase())
```

### Use Case 3: News Article with External Images

```python
async def news_article():
    async with create_wordpress_client('test') as client:
        # News images from reliable sources
        news_images = [
            {
                'url': 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800',
                'title': 'News Header Image',
                'alt_text': 'Breaking news illustration',
                'is_featured': True
            }
        ]
        
        content = """
        <h2>Breaking: Important News Update</h2>
        <p><em>Published: Today</em></p>
        
        <img src="https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=800" 
             alt="News illustration" style="width: 100%; margin: 20px 0;">
        
        <p>This is an important news update that affects our community...</p>
        
        <h3>Key Points</h3>
        <ul>
            <li>Important development #1</li>
            <li>Critical update #2</li>
            <li>Action required #3</li>
        </ul>
        
        <p><strong>Stay tuned for more updates.</strong></p>
        """
        
        post = await client.create_post_with_external_images(
            title="Breaking News: Important Update",
            content=content,
            external_images=news_images,
            categories=['News', 'Updates'],
            tags=['breaking-news', 'update', 'important'],
            status='published'  # Publish immediately for news
        )
        
        print(f"✅ News article published: {post['id']}")

asyncio.run(news_article())
```

## ⚡ Performance Tips

### 1. Batch Processing for Multiple Images

```python
async def batch_processing_example():
    async with create_wordpress_client('test') as client:
        media_handler = client.get_media_handler()
        
        # Process multiple images at once
        image_urls = [
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
            'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400',
            'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?w=400'
        ]
        
        # Batch process with concurrency control
        results = await media_handler.batch_process_external_urls(
            urls=image_urls,
            method='direct_url',
            max_concurrent=3  # Process 3 images simultaneously
        )
        
        successful = [r for r in results if 'error' not in r]
        print(f"✅ Processed {len(successful)}/{len(image_urls)} images successfully")

asyncio.run(batch_processing_example())
```

### 2. URL Validation Before Processing

```python
async def validate_before_processing():
    async with create_wordpress_client('test') as client:
        media_handler = client.get_media_handler()
        
        test_urls = [
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
            'https://invalid-domain.com/image.jpg'
        ]
        
        valid_urls = []
        
        for url in test_urls:
            validation = await media_handler.validate_external_image_url(url)
            if validation['is_valid'] and validation['is_accessible']:
                valid_urls.append(url)
                print(f"✅ Valid: {url}")
            else:
                print(f"❌ Invalid: {url} - {validation.get('error', 'Not accessible')}")
        
        print(f"Found {len(valid_urls)} valid URLs out of {len(test_urls)}")

asyncio.run(validate_before_processing())
```

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Issue 1: "URL validation failed"
```python
# Solution: Check URL accessibility
async def debug_url_validation():
    async with create_wordpress_client('test') as client:
        media_handler = client.get_media_handler()
        
        problematic_url = "https://example.com/image.jpg"
        
        try:
            result = await media_handler.validate_external_image_url(problematic_url)
            print(f"Validation result: {result}")
        except Exception as e:
            print(f"Validation error: {e}")
            # Use fallback URL
            fallback = await media_handler.url_handler.get_fallback_url(problematic_url)
            print(f"Using fallback: {fallback}")
```

#### Issue 2: "Processing timeout"
```python
# Solution: Adjust timeout settings in config.yaml
wordpress:
  media:
    image_handling:
      direct_url:
        validation:
          timeout_seconds: 30  # Increase timeout
```

#### Issue 3: "Domain blocked"
```python
# Solution: Update domain restrictions in config.yaml
wordpress:
  media:
    image_handling:
      direct_url:
        validation:
          blocked_domains: []  # Remove domain blocks
          allowed_domains: ["unsplash.com", "images.unsplash.com"]  # Or specify allowed domains
```

## 🔄 Method Switching

### Switch from Direct URL to Cloud Storage

1. **Configure cloud storage** in `config.yaml`
2. **Test the configuration**:

```python
async def test_cloud_storage():
    async with create_wordpress_client('test') as client:
        media_handler = client.get_media_handler()
        
        # Check available methods
        methods = await media_handler.get_image_handling_methods()
        print("Available methods:", list(methods['methods'].keys()))
        
        if methods['methods']['cloud_storage']['enabled']:
            print("✅ Cloud storage is ready")
        else:
            print("❌ Cloud storage not configured")

asyncio.run(test_cloud_storage())
```

3. **Update default method**:
```yaml
wordpress:
  media:
    image_handling:
      default_method: cloud_storage  # Changed from direct_url
```

## 📊 Performance Monitoring

### Monitor Processing Performance

```python
async def monitor_performance():
    import time
    
    async with create_wordpress_client('test') as client:
        media_handler = client.get_media_handler()
        
        test_url = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400"
        
        # Test different methods
        methods = ['direct_url', 'local_upload']
        
        for method in methods:
            start_time = time.time()
            
            try:
                result = await media_handler.process_external_image_url(
                    url=test_url,
                    method=method,
                    title=f"Performance Test - {method}"
                )
                
                processing_time = time.time() - start_time
                print(f"{method}: {processing_time:.2f}s")
                
            except Exception as e:
                print(f"{method}: Failed - {e}")

asyncio.run(monitor_performance())
```

## 🎉 Next Steps

1. **Try the examples** above with your own images
2. **Run the demo scripts** in `utils/wordpress/example/`
3. **Configure cloud storage** for production use
4. **Set up monitoring** for URL accessibility
5. **Integrate with your content generation agents**

For more advanced features and configuration options, see the [complete documentation](wordpress-external-image-handling.md).

## 📞 Getting Help

- **Run tests**: `python tests/run_external_image_tests.py --all`
- **Check examples**: Look in `utils/wordpress/example/` directory
- **Enable debug logging**: Set log level to DEBUG in your application
- **Review configuration**: Ensure all required settings are properly configured
